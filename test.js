// Simple calculator functions for testing aider-ts

function add(a, b) {
    return a + b;
}

function subtract(a, b) {
    return a - b;
}

function multiply(a, b) {
    return a * b;
}

function divide(a, b) {
    return a / b;
}

// Main calculator function
function calculator(operation, num1, num2) {
    switch(operation) {
        case 'add':
            return add(num1, num2);
        case 'subtract':
            return subtract(num1, num2);
        case 'multiply':
            return multiply(num1, num2);
        case 'divide':
            return divide(num1, num2);
        default:
            return 'Invalid operation';
    }
}

// Example usage
console.log(calculator('add', 5, 3));
console.log(calculator('multiply', 4, 7));

module.exports = {
    add,
    subtract,
    multiply,
    divide,
    calculator
};
