#!/usr/bin/env node

const path = require('path');
const fs = require('fs');

// Get the directory where this script is located
const binDir = __dirname;
const rootDir = path.dirname(binDir);
const distDir = path.join(rootDir, 'dist');
const mainFile = path.join(distDir, 'main.js');

// Check if the compiled file exists
if (!fs.existsSync(mainFile)) {
  console.error('Error: aider-ts has not been built yet.');
  console.error('Please run: npm run build');
  process.exit(1);
}

// Require and run the main application
try {
  require(mainFile);
} catch (error) {
  console.error('Error running aider-ts:', error.message);
  process.exit(1);
}
