import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';
import { queryManager, QueryResult, TreeSitterQueryManager } from '../queries';
import { GitRepo } from '../repo';

export interface FileMap {
  filePath: string;
  language: string;
  definitions: QueryResult[];
  references: QueryResult[];
  size: number;
  lastModified: Date;
}

export interface RepoMapOptions {
  maxFileSize?: number;
  excludePatterns?: string[];
  includePatterns?: string[];
  maxDepth?: number;
  followSymlinks?: boolean;
  useGitIgnore?: boolean;
}

export interface RepoSummary {
  totalFiles: number;
  totalLines: number;
  totalSize: number;
  languageBreakdown: Record<string, number>;
  definitionCounts: Record<string, number>;
  topLevelModules: string[];
  entryPoints: string[];
}

export class RepoMap {
  private fileMap: Map<string, FileMap> = new Map();
  private queryManager: TreeSitterQueryManager;
  private gitRepo?: GitRepo;
  private options: RepoMapOptions;

  private readonly defaultExcludePatterns = [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    '**/.git/**',
    '**/coverage/**',
    '**/*.min.js',
    '**/*.map',
    '**/vendor/**',
    '**/target/**',
    '**/__pycache__/**',
    '**/*.pyc',
    '**/venv/**',
    '**/env/**',
    '**/.next/**',
    '**/.nuxt/**',
    '**/out/**',
  ];

  constructor(
    private rootPath: string,
    options: RepoMapOptions = {}
  ) {
    this.options = {
      maxFileSize: options.maxFileSize || 1024 * 1024, // 1MB default
      excludePatterns: [...this.defaultExcludePatterns, ...(options.excludePatterns || [])],
      includePatterns: options.includePatterns || ['**/*'],
      maxDepth: options.maxDepth || 10,
      followSymlinks: options.followSymlinks || false,
      useGitIgnore: options.useGitIgnore ?? true,
    };

    this.queryManager = queryManager;
    this.initializeGitRepo();
  }

  private initializeGitRepo(): void {
    try {
      this.gitRepo = new GitRepo({
        io: null, // We don't need IO for basic git operations
        fnames: [this.rootPath],
      });
    } catch (error) {
      // Not a git repository, continue without git integration
      console.warn('Not a git repository, continuing without git integration');
    }
  }

  public async buildMap(): Promise<void> {
    console.log(`Building repository map for: ${this.rootPath}`);

    const files = await this.findSourceFiles();
    console.log(`Found ${files.length} source files`);

    const processedFiles = await this.processFiles(files);
    console.log(`Successfully processed ${processedFiles} files`);
  }

  private async findSourceFiles(): Promise<string[]> {
    const globPatterns = this.options.includePatterns!;
    const allFiles: string[] = [];

    for (const pattern of globPatterns) {
      try {
        const files = await glob(pattern, {
          cwd: this.rootPath,
          ignore: this.options.excludePatterns,
          follow: this.options.followSymlinks,
          maxDepth: this.options.maxDepth,
          absolute: true,
        });
        allFiles.push(...files);
      } catch (error) {
        console.warn(`Error processing glob pattern ${pattern}:`, error);
      }
    }

    // Remove duplicates and filter
    const uniqueFiles = Array.from(new Set(allFiles));
    return this.filterFiles(uniqueFiles);
  }

  private filterFiles(files: string[]): string[] {
    return files.filter(file => {
      try {
        const stats = fs.statSync(file);

        // Skip directories
        if (stats.isDirectory()) return false;

        // Skip large files
        if (stats.size > this.options.maxFileSize!) return false;

        // Skip binary files (basic heuristic)
        if (this.isBinaryFile(file)) return false;

        // Check if file is supported by tree-sitter
        const language = this.queryManager.getLanguageFromFileName(file);
        if (!language || !this.queryManager.isLanguageSupported(language)) {
          return false;
        }

        // Use git ignore if available
        if (this.options.useGitIgnore && this.gitRepo) {
          const relativePath = path.relative(this.rootPath, file);
          if (this.gitRepo.ignoredFile(relativePath)) {
            return false;
          }
        }

        return true;
      } catch (error) {
        return false;
      }
    });
  }

  private isBinaryFile(filePath: string): boolean {
    const binaryExtensions = [
      '.exe', '.dll', '.so', '.dylib', '.bin', '.dat', '.db',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.zip', '.tar', '.gz', '.rar', '.7z',
      '.mp3', '.mp4', '.avi', '.mov', '.wmv',
      '.ttf', '.otf', '.woff', '.woff2',
    ];

    const ext = path.extname(filePath).toLowerCase();
    return binaryExtensions.includes(ext);
  }

  private async processFiles(files: string[]): Promise<number> {
    let processed = 0;
    const batchSize = 10;

    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      await Promise.all(batch.map(file => this.processFile(file)));
      processed += batch.length;

      if (processed % 50 === 0) {
        console.log(`Processed ${processed}/${files.length} files`);
      }
    }

    return processed;
  }

  private async processFile(filePath: string): Promise<void> {
    try {
      const stats = fs.statSync(filePath);
      const content = fs.readFileSync(filePath, 'utf-8');
      const language = this.queryManager.getLanguageFromFileName(filePath)!;

      const definitions = this.queryManager.getDefinitions(filePath, content);
      const references = this.queryManager.getReferences(filePath, content);

      const fileMap: FileMap = {
        filePath: path.relative(this.rootPath, filePath),
        language,
        definitions,
        references,
        size: stats.size,
        lastModified: stats.mtime,
      };

      this.fileMap.set(fileMap.filePath, fileMap);
    } catch (error) {
      console.warn(`Failed to process file ${filePath}:`, error);
    }
  }

  public getFileMap(filePath: string): FileMap | undefined {
    return this.fileMap.get(filePath);
  }

  public getAllFiles(): FileMap[] {
    return Array.from(this.fileMap.values());
  }

  public getFilesByLanguage(language: string): FileMap[] {
    return this.getAllFiles().filter(file => file.language === language);
  }

  public searchDefinitions(query: string): QueryResult[] {
    const results: QueryResult[] = [];
    const lowerQuery = query.toLowerCase();

    for (const fileMap of this.fileMap.values()) {
      const matches = fileMap.definitions.filter(def =>
        def.name.toLowerCase().includes(lowerQuery)
      );
      results.push(...matches);
    }

    return results.sort((a, b) => a.name.localeCompare(b.name));
  }

  public findDefinition(name: string, category?: string): QueryResult[] {
    const results: QueryResult[] = [];

    for (const fileMap of this.fileMap.values()) {
      const matches = fileMap.definitions.filter(def => {
        const nameMatch = def.name === name;
        const categoryMatch = !category || def.category === category;
        return nameMatch && categoryMatch;
      });
      results.push(...matches);
    }

    return results;
  }

  public getReferencesTo(name: string): QueryResult[] {
    const results: QueryResult[] = [];

    for (const fileMap of this.fileMap.values()) {
      const matches = fileMap.references.filter(ref => ref.name === name);
      results.push(...matches);
    }

    return results;
  }

  public generateSummary(): RepoSummary {
    const files = this.getAllFiles();
    const languageBreakdown: Record<string, number> = {};
    const definitionCounts: Record<string, number> = {};
    let totalLines = 0;
    let totalSize = 0;

    for (const file of files) {
      // Language breakdown
      languageBreakdown[file.language] = (languageBreakdown[file.language] || 0) + 1;

      // Definition counts
      for (const def of file.definitions) {
        const key = `${def.category}`;
        definitionCounts[key] = (definitionCounts[key] || 0) + 1;
      }

      // Size and lines
      totalSize += file.size;
      try {
        const content = fs.readFileSync(path.join(this.rootPath, file.filePath), 'utf-8');
        totalLines += content.split('\n').length;
      } catch (error) {
        // File might have been deleted/moved
      }
    }

    const topLevelModules = this.findTopLevelModules();
    const entryPoints = this.findEntryPoints();

    return {
      totalFiles: files.length,
      totalLines,
      totalSize,
      languageBreakdown,
      definitionCounts,
      topLevelModules,
      entryPoints,
    };
  }

  private findTopLevelModules(): string[] {
    const modules = new Set<string>();

    for (const file of this.fileMap.values()) {
      for (const def of file.definitions) {
        if (def.category === 'module' || def.category === 'class') {
          modules.add(def.name);
        }
      }
    }

    return Array.from(modules).sort();
  }

  private findEntryPoints(): string[] {
    const entryPoints: string[] = [];
    const commonEntryNames = [
      'main', 'index', 'app', 'server', 'client',
      '__main__', 'Main', 'Application'
    ];

    for (const file of this.fileMap.values()) {
      // Check for common entry point file names
      const fileName = path.basename(file.filePath, path.extname(file.filePath));
      if (commonEntryNames.includes(fileName)) {
        entryPoints.push(file.filePath);
        continue;
      }

      // Check for main functions
      const hasMainFunction = file.definitions.some(def =>
        def.category === 'function' && commonEntryNames.includes(def.name)
      );

      if (hasMainFunction) {
        entryPoints.push(file.filePath);
      }
    }

    return entryPoints;
  }

  public exportToJSON(): string {
    const data = {
      rootPath: this.rootPath,
      summary: this.generateSummary(),
      files: Array.from(this.fileMap.entries()).map(([path, fileMap]) => ({
        path,
        ...fileMap,
        definitions: fileMap.definitions.map(def => ({
          name: def.name,
          type: def.type,
          category: def.category,
          startPosition: def.startPosition,
          endPosition: def.endPosition,
        })),
        references: fileMap.references.map(ref => ({
          name: ref.name,
          type: ref.type,
          category: ref.category,
          startPosition: ref.startPosition,
          endPosition: ref.endPosition,
        })),
      })),
      generatedAt: new Date().toISOString(),
    };

    return JSON.stringify(data, null, 2);
  }

  public async saveToFile(outputPath: string): Promise<void> {
    const json = this.exportToJSON();
    fs.writeFileSync(outputPath, json, 'utf-8');
    console.log(`Repository map saved to: ${outputPath}`);
  }

  public getStats(): Record<string, any> {
    const summary = this.generateSummary();
    const files = this.getAllFiles();

    return {
      files: {
        total: summary.totalFiles,
        byLanguage: summary.languageBreakdown,
        averageSize: Math.round(summary.totalSize / summary.totalFiles),
      },
      definitions: {
        total: Object.values(summary.definitionCounts).reduce((a, b) => a + b, 0),
        byCategory: summary.definitionCounts,
      },
      codebase: {
        totalLines: summary.totalLines,
        totalSize: summary.totalSize,
        entryPoints: summary.entryPoints.length,
        topLevelModules: summary.topLevelModules.length,
      },
    };
  }

  public clear(): void {
    this.fileMap.clear();
  }
}

// Utility functions
export async function createRepoMap(
  rootPath: string,
  options?: RepoMapOptions
): Promise<RepoMap> {
  const repoMap = new RepoMap(rootPath, options);
  await repoMap.buildMap();
  return repoMap;
}

export async function analyzeRepository(rootPath: string): Promise<RepoSummary> {
  const repoMap = await createRepoMap(rootPath);
  return repoMap.generateSummary();
}

export async function findDefinitionInRepo(
  rootPath: string,
  name: string,
  category?: string
): Promise<QueryResult[]> {
  const repoMap = await createRepoMap(rootPath);
  return repoMap.findDefinition(name, category);
}

export default RepoMap;
