#!/usr/bin/env node

import chalk from 'chalk';
import figlet from 'figlet';
import { Command } from 'commander';
import { __version__ } from '../version';

// Simple, working CLI interface inspired by Claude
class SimpleCLI {
  private program: Command;

  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  private setupCommands(): void {
    this.program
      .name('aider')
      .description('🤖 AI pair programming assistant')
      .version(__version__)
      .option('-v, --verbose', 'Enable verbose output')
      .option('-q, --quiet', 'Suppress banner and non-essential output');

    // Main chat command (default)
    this.program
      .argument('[files...]', 'Files to include in the chat context')
      .option('-m, --model <model>', 'AI model to use (e.g., gpt-4o, claude-3-5-sonnet)')
      .option('--message <message>', 'Send a single message')
      .option('--auto-commit', 'Automatically commit changes')
      .option('--dry-run', 'Show what would be done without making changes')
      .option('--list-models', 'List available models')
      .option('--test-model <model>', 'Test model connectivity')
      .action(this.handleMain.bind(this));

    // Setup command
    this.program
      .command('setup')
      .description('Interactive setup wizard')
      .action(this.handleSetup.bind(this));

    // Config command
    this.program
      .command('config')
      .description('Show configuration help')
      .action(this.handleConfig.bind(this));
  }

  private showBanner(): void {
    if (process.argv.includes('--quiet') || process.argv.includes('-q')) {
      return;
    }

    try {
      const banner = figlet.textSync('aider', {
        font: 'Small',
        horizontalLayout: 'default',
        verticalLayout: 'default'
      });
      console.log(chalk.cyan(banner));
    } catch {
      console.log(chalk.cyan.bold('🤖 aider'));
    }
    
    console.log(chalk.gray(`v${__version__} • AI pair programming assistant\n`));
  }

  private async handleMain(files: string[], options: any): Promise<void> {
    this.showBanner();

    if (options.listModels) {
      this.showAvailableModels();
      return;
    }

    if (options.testModel) {
      await this.testModel(options.testModel);
      return;
    }

    // Check for API keys
    const apiKeys = this.checkApiKeys();
    if (apiKeys.length === 0) {
      this.showApiKeyHelp();
      return;
    }

    // Show session info
    this.showSessionInfo(options, files, apiKeys);

    // Import and run the original main function
    try {
      const { main } = await import('../main');
      
      const args = {
        files: files || [],
        model: options.model || this.getDefaultModel(apiKeys),
        message: options.message,
        auto_commits: options.autoCommit,
        dry_run: options.dryRun,
        verbose: options.verbose,
        version: false,
        analytics: null,
        analytics_log: null,
        analytics_disable: false,
        posthog_host: null,
        posthog_project_api_key: null,
        show_diffs: true,
        stream: true,
        auto_lint: false,
        auto_test: false,
        test_cmd: null,
        lint_cmd: [],
        read_only: [],
        edit_format: null
      };

      await main({ args });
    } catch (error) {
      console.error(chalk.red(`Error: ${error instanceof Error ? error.message : error}`));
      process.exit(1);
    }
  }

  private async handleSetup(): Promise<void> {
    console.log(chalk.cyan.bold('\n🚀 Aider Setup Wizard\n'));
    
    console.log('Welcome to aider! Let\'s get you set up with AI pair programming.\n');
    
    console.log(chalk.yellow('Step 1: Get an API Key'));
    console.log('Choose one of these AI providers:\n');
    
    console.log(chalk.blue('🤖 OpenAI (Recommended)'));
    console.log('  • Models: GPT-4o, GPT-4o Mini, GPT-4 Turbo');
    console.log('  • Get key: https://platform.openai.com/api-keys');
    console.log('  • Set: export OPENAI_API_KEY=your_key_here\n');
    
    console.log(chalk.magenta('🧠 Anthropic'));
    console.log('  • Models: Claude 3.5 Sonnet, Claude 3 Haiku');
    console.log('  • Get key: https://console.anthropic.com/');
    console.log('  • Set: export ANTHROPIC_API_KEY=your_key_here\n');
    
    console.log(chalk.green('⚡ xAI'));
    console.log('  • Models: Grok Beta, Grok 3 Mini');
    console.log('  • Get key: https://console.x.ai/');
    console.log('  • Set: export XAI_API_KEY=your_key_here\n');
    
    console.log(chalk.yellow('Step 2: Run Aider'));
    console.log('Once you have an API key set up:');
    console.log(chalk.cyan('  aider                    ') + '# Start interactive session');
    console.log(chalk.cyan('  aider file1.js file2.py ') + '# Include specific files');
    console.log(chalk.cyan('  aider --model gpt-4o     ') + '# Use specific model');
    console.log(chalk.cyan('  aider --message "help"   ') + '# Send a message\n');
    
    console.log(chalk.green('✅ Setup complete! Set your API key and start coding with AI.'));
  }

  private handleConfig(): void {
    console.log(chalk.cyan.bold('\n⚙️  Aider Configuration\n'));
    
    console.log(chalk.yellow('Environment Variables:'));
    console.log('  OPENAI_API_KEY      - OpenAI API key');
    console.log('  ANTHROPIC_API_KEY   - Anthropic API key');
    console.log('  XAI_API_KEY         - xAI API key');
    console.log('  AIDER_MODEL         - Default model to use\n');
    
    console.log(chalk.yellow('Current Status:'));
    const apiKeys = this.checkApiKeys();
    if (apiKeys.length > 0) {
      console.log(chalk.green('✅ API keys found for: ' + apiKeys.join(', ')));
    } else {
      console.log(chalk.red('❌ No API keys found'));
    }
    
    console.log('\n' + chalk.yellow('Common Commands:'));
    console.log('  aider --help            # Show all options');
    console.log('  aider --list-models     # List available models');
    console.log('  aider --test-model gpt-4o # Test model connectivity');
  }

  private checkApiKeys(): string[] {
    const keys = [];
    if (process.env.OPENAI_API_KEY) keys.push('OpenAI');
    if (process.env.ANTHROPIC_API_KEY) keys.push('Anthropic');
    if (process.env.XAI_API_KEY) keys.push('xAI');
    return keys;
  }

  private showApiKeyHelp(): void {
    console.log(chalk.red.bold('❌ No API keys found!\n'));
    
    console.log('You need to set an API key to use aider. Choose one:\n');
    
    console.log(chalk.blue('For OpenAI (Recommended):'));
    console.log('  export OPENAI_API_KEY=your_openai_key\n');
    
    console.log(chalk.magenta('For Anthropic:'));
    console.log('  export ANTHROPIC_API_KEY=your_anthropic_key\n');
    
    console.log(chalk.green('For xAI:'));
    console.log('  export XAI_API_KEY=your_xai_key\n');
    
    console.log(chalk.cyan('Run ') + chalk.bold('aider setup') + chalk.cyan(' for detailed instructions.'));
  }

  private showAvailableModels(): void {
    console.log(chalk.cyan.bold('\n🤖 Available Models\n'));
    
    console.log(chalk.blue('OpenAI Models:'));
    console.log('  gpt-4o              # Latest GPT-4 Omni (Recommended)');
    console.log('  gpt-4o-mini         # Fast and efficient');
    console.log('  gpt-4-turbo         # Previous generation');
    console.log('  gpt-3.5-turbo       # Budget option\n');
    
    console.log(chalk.magenta('Anthropic Models:'));
    console.log('  claude-3-5-sonnet-20241022  # Latest Claude (Recommended)');
    console.log('  claude-3-opus-20240229      # Most capable');
    console.log('  claude-3-sonnet-20240229    # Balanced');
    console.log('  claude-3-haiku-20240307     # Fast and efficient\n');
    
    console.log(chalk.green('xAI Models:'));
    console.log('  grok-beta           # Latest Grok');
    console.log('  grok-3              # Grok 3');
    console.log('  grok-3-mini         # Efficient Grok\n');
    
    console.log(chalk.yellow('Usage:'));
    console.log('  aider --model gpt-4o');
    console.log('  aider --model claude-3-5-sonnet-20241022');
    console.log('  aider --model grok-beta');
  }

  private async testModel(model: string): Promise<void> {
    console.log(chalk.yellow(`Testing ${model}...`));
    
    // Simple connectivity test
    const apiKeys = this.checkApiKeys();
    if (apiKeys.length === 0) {
      console.log(chalk.red('❌ No API keys found. Cannot test model.'));
      return;
    }
    
    console.log(chalk.green(`✅ API keys available. Model ${model} should work.`));
    console.log(chalk.gray('Note: Full model testing requires running an actual chat session.'));
  }

  private getDefaultModel(apiKeys: string[]): string {
    if (process.env.AIDER_MODEL) {
      return process.env.AIDER_MODEL;
    }
    
    if (apiKeys.includes('OpenAI')) {
      return 'gpt-4o';
    } else if (apiKeys.includes('Anthropic')) {
      return 'claude-3-5-sonnet-20241022';
    } else if (apiKeys.includes('xAI')) {
      return 'grok-beta';
    }
    
    return 'gpt-4o'; // fallback
  }

  private showSessionInfo(options: any, files: string[], apiKeys: string[]): void {
    const model = options.model || this.getDefaultModel(apiKeys);
    
    console.log(chalk.blue('┌─ Session Info ─────────────────────────────────────────┐'));
    console.log(chalk.blue('│') + ` Model: ${chalk.bold(model)}`.padEnd(55) + chalk.blue('│'));
    console.log(chalk.blue('│') + ` Files: ${files.length > 0 ? files.join(', ') : 'Auto-detect'}`.padEnd(55) + chalk.blue('│'));
    console.log(chalk.blue('│') + ` Providers: ${apiKeys.join(', ')}`.padEnd(55) + chalk.blue('│'));
    console.log(chalk.blue('└────────────────────────────────────────────────────────┘\n'));
    
    if (options.message) {
      console.log(chalk.yellow('Sending message: ') + options.message + '\n');
    }
  }

  public async run(): Promise<void> {
    try {
      await this.program.parseAsync();
    } catch (error) {
      console.error(chalk.red(`Error: ${error instanceof Error ? error.message : error}`));
      process.exit(1);
    }
  }
}

// CLI entry point
async function main() {
  const cli = new SimpleCLI();
  await cli.run();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.gray('\n👋 Goodbye!'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.gray('\n👋 Goodbye!'));
  process.exit(0);
});

if (require.main === module) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { main };
export default SimpleCLI;