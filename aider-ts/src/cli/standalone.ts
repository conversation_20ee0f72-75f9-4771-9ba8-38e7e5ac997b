#!/usr/bin/env node

import chalk from 'chalk';
import figlet from 'figlet';
import { Command } from 'commander';
import inquirer from 'inquirer';
import autocompletePrompt from 'inquirer-autocomplete-prompt';
import fuzzy from 'fuzzy';
import boxen from 'boxen';
const Table = require('cli-table3');
import fs from 'fs';
import path from 'path';
import os from 'os';

// Register autocomplete prompt
inquirer.registerPrompt('autocomplete', autocompletePrompt);

// Version constant (standalone)
const __version__ = '0.85.3-dev';

// Configuration
const CONFIG_FILE = path.join(os.homedir(), '.aider-cli-config.json');

// Command definitions for autocomplete (complete Claude Code command set)
const COMMANDS = [
  {
    name: '/add-dir',
    description: 'Add a new working directory',
    aliases: ['add-dir']
  },
  {
    name: '/agents',
    description: 'Manage agent configurations',
    aliases: ['agents']
  },
  {
    name: '/bug',
    description: 'Submit feedback about Claude Code',
    aliases: ['bug']
  },
  {
    name: '/clear',
    description: 'Clear conversation history and free up context',
    aliases: ['clear', 'reset']
  },
  {
    name: '/compact',
    description: 'Clear conversation history but keep a summary in context. Optional: /compact [instructions for summarization]',
    aliases: ['compact']
  },
  {
    name: '/config',
    description: 'Open config panel',
    aliases: ['config', 'theme']
  },
  {
    name: '/cost',
    description: 'Show the total cost and duration of the current session',
    aliases: ['cost']
  },
  {
    name: '/doctor',
    description: 'Diagnose and verify your Claude Code installation and settings',
    aliases: ['doctor']
  },
  {
    name: '/exit',
    description: 'Exit the REPL',
    aliases: ['exit', 'quit']
  },
  {
    name: '/export',
    description: 'Export the current conversation to a file or clipboard',
    aliases: ['export']
  },
  {
    name: '/help',
    description: 'Show help and available commands',
    aliases: ['help']
  },
  {
    name: '/hooks',
    description: 'Manage hook configurations for tool events',
    aliases: ['hooks']
  },
  {
    name: '/ide',
    description: 'Manage IDE integrations and show status',
    aliases: ['ide']
  },
  {
    name: '/init',
    description: 'Initialize a new CLAUDE.md file with codebase documentation',
    aliases: ['init']
  },
  {
    name: '/install-github-app',
    description: 'Set up Claude GitHub Actions for a repository',
    aliases: ['install-github-app']
  },
  {
    name: '/login',
    description: 'Sign in with your Anthropic account',
    aliases: ['login']
  },
  {
    name: '/logout',
    description: 'Sign out from your Anthropic account',
    aliases: ['logout']
  },
  {
    name: '/mcp',
    description: 'Manage MCP servers',
    aliases: ['mcp']
  },
  {
    name: '/memory',
    description: 'Edit Claude memory files',
    aliases: ['memory']
  },
  {
    name: '/migrate-installer',
    description: 'Migrate from global npm installation to local installation',
    aliases: ['migrate-installer']
  },
  {
    name: '/model',
    description: 'Set the AI model for Claude Code',
    aliases: ['model']
  },
  {
    name: '/permissions',
    description: 'Manage allow & deny tool permission rules',
    aliases: ['permissions', 'allowed-tools']
  },
  {
    name: '/pr-comments',
    description: 'Get comments from a GitHub pull request',
    aliases: ['pr-comments']
  },
  {
    name: '/release-notes',
    description: 'View release notes',
    aliases: ['release-notes']
  },
  {
    name: '/resume',
    description: 'Resume a conversation',
    aliases: ['resume']
  },
  {
    name: '/review',
    description: 'Review a pull request',
    aliases: ['review']
  },
  {
    name: '/status',
    description: 'Show Claude Code status including version, model, account, API connectivity, and tool statuses',
    aliases: ['status']
  },
  {
    name: '/theme',
    description: 'Change color theme',
    aliases: ['theme']
  },
  {
    name: '/upgrade',
    description: 'Upgrade to Max for higher rate limits and more Opus',
    aliases: ['upgrade']
  },
  {
    name: '/vim',
    description: 'Toggle between Vim and Normal editing modes',
    aliases: ['vim']
  }
];

// Configuration interface
interface Config {
  model: string;
  apiTimeout: number;
  apiBaseUrl: string;
  theme: string;
  vimMode: boolean;
}

// Default configuration
let config: Config = {
  model: 'gpt-4',
  apiTimeout: 30000,
  apiBaseUrl: 'https://api.openai.com/v1',
  theme: 'dark',
  vimMode: false
};

// Global state
let currentSession: any = null;
let sessionHistory: any[] = [];

// Load configuration
function loadConfig(): void {
  try {
    if (fs.existsSync(CONFIG_FILE)) {
      const savedConfig = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
      config = { ...config, ...savedConfig };
    }
  } catch (error) {
    // Use default config if loading fails
  }
}

// Save configuration
function saveConfig(): void {
  try {
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error(chalk.red('Failed to save configuration'));
  }
}

// Enhanced CLI interface inspired by Claude
class StandaloneCLI {
  private program: Command;

  constructor() {
    this.program = new Command();
    this.setupCommands();
    this.loadConfig();
  }

  // Load configuration
  private loadConfig(): void {
    try {
      if (fs.existsSync(CONFIG_FILE)) {
        const savedConfig = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
        config = { ...config, ...savedConfig };
      }
    } catch (error) {
      // Use default config if loading fails
    }
  }

  // Save configuration
  private saveConfig(): void {
    try {
      fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    } catch (error) {
      console.error(chalk.red('Failed to save configuration'));
    }
  }

  // Autocomplete search function
  private searchCommands(answers: any, input: string = ''): Promise<any[]> {
    return new Promise((resolve) => {
      // If input starts with '/', show command suggestions
      if (input.startsWith('/')) {
        const searchTerm = input.slice(1).toLowerCase();
        
        // Quick exit for performance
        if (searchTerm.length === 0) {
          const topCommands = COMMANDS.slice(0, 5).map(cmd => ({
            name: `${cmd.name} - ${chalk.gray(cmd.description)}`,
            value: cmd.name
          }));
          resolve(topCommands);
          return;
        }
        
        const results = fuzzy.filter(searchTerm, COMMANDS, {
          extract: (cmd) => cmd.name.slice(1) + ' ' + cmd.description
        });
        
        const suggestions = results.slice(0, 5).map(result => ({
          name: `${result.original.name} - ${chalk.gray(result.original.description)}`,
          value: result.original.name
        }));
        
        resolve(suggestions);
      } else {
        // For regular input, just return the input option
        resolve([{
          name: input || 'Type your message or use / for commands',
          value: input
        }]);
      }
    });
  }

  // Smart input handler with command suggestions
  private async getSmartInput(): Promise<string> {
    try {
      // Use simple input to avoid backspace lag
      const { input } = await inquirer.prompt([
        {
          type: 'input',
          name: 'input',
          message: chalk.cyan('> '),
          prefix: ''
        }
      ]);
      
      // Show command suggestions after input if it's a partial slash command
      if (input.startsWith('/') && !input.includes(' ') && input.length > 1) {
        const searchTerm = input.slice(1).toLowerCase();
        const matches = COMMANDS.filter(cmd => 
          cmd.name.slice(1).toLowerCase().startsWith(searchTerm) ||
          cmd.description.toLowerCase().includes(searchTerm)
        );
        
        if (matches.length > 0 && !COMMANDS.find(cmd => cmd.name === input)) {
          console.log(chalk.gray('\nDid you mean:'));
          matches.slice(0, 5).forEach(cmd => {
            console.log(chalk.cyan(`  ${cmd.name}`) + chalk.gray(` - ${cmd.description}`));
          });
          console.log('');
        }
      }
      
      return input;
    } catch (error: any) {
      if (error.name === 'ExitPromptError') {
        throw error;
      }
      
      console.error(chalk.red(`Input error: ${error.message}`));
      return '';
    }
  }

  private setupCommands(): void {
    this.program
      .name('aider')
      .description('🤖 AI pair programming assistant')
      .version(__version__)
      .option('-v, --verbose', 'Enable verbose output')
      .option('-q, --quiet', 'Suppress banner and non-essential output');

    // Main chat command (default)
    this.program
      .argument('[files...]', 'Files to include in the chat context')
      .option('-m, --model <model>', 'AI model to use (e.g., gpt-4o, claude-3-5-sonnet)')
      .option('--message <message>', 'Send a single message')
      .option('--auto-commit', 'Automatically commit changes')
      .option('--dry-run', 'Show what would be done without making changes')
      .option('--list-models', 'List available models')
      .option('--test-model <model>', 'Test model connectivity')
      .action(this.handleMain.bind(this));

    // Setup command
    this.program
      .command('setup')
      .description('Interactive setup wizard')
      .action(this.handleSetup.bind(this));

    // Config command
    this.program
      .command('config')
      .description('Show configuration help')
      .action(this.handleConfig.bind(this));
  }

  // Display Claude-style welcome banner
  private showWelcomeBanner(): void {
    if (process.argv.includes('--quiet') || process.argv.includes('-q')) {
      return;
    }

    console.clear();
    
    // ASCII art banner
    try {
      const banner = figlet.textSync('Aider CLI', {
        font: 'Small',
        horizontalLayout: 'default',
        verticalLayout: 'default'
      });
      console.log(chalk.cyan(banner));
    } catch {
      console.log(chalk.cyan.bold('🤖 Aider CLI'));
    }
    
    console.log(chalk.gray('AI pair programming assistant - Claude-like interface\n'));
    
    // Welcome box similar to Claude
    const welcomeText = `${chalk.yellow('★')} ${chalk.bold('Welcome to Aider CLI!')}

${chalk.gray('/help for help, /status for your current setup')}

${chalk.bold('cwd:')} ${process.cwd()}

${chalk.gray('─'.repeat(50))}

${chalk.bold('Overrides (via env):')}
${chalk.gray('•')} API timeout: ${chalk.cyan(config.apiTimeout + 'ms')}
${chalk.gray('•')} API Base URL: ${chalk.cyan(config.apiBaseUrl)}`;
    
    const welcomeBox = boxen(welcomeText, {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'yellow',
      backgroundColor: '#1a1a1a'
    });
    
    console.log(welcomeBox);
    
    // Tip
    console.log(chalk.gray(`${chalk.yellow('★')} Tip: Use ${chalk.cyan('/theme')} to change the color theme\n`));
  }

  // Display command help
  private showHelp(): void {
    const commands = [
      ['/add-dir', 'Add a new working directory'],
      ['/agents', 'Manage agent configurations'],
      ['/bug', 'Submit feedback about Aider CLI'],
      ['/clear (reset)', 'Clear conversation history and free up context'],
      ['/compact', 'Clear conversation history but keep a summary in context'],
      ['', 'Optional: /compact [instructions for summarization]'],
      ['/config (theme)', 'Open config panel'],
      ['/cost', 'Show the total cost and duration of the current session'],
      ['/doctor', 'Diagnose and verify your Aider CLI installation and settings'],
      ['/exit (quit)', 'Exit the REPL'],
      ['/export', 'Export the current conversation to a file or clipboard'],
      ['/help', 'Show this help message'],
      ['/model', 'Change the AI model'],
      ['/status', 'Show current session status'],
      ['/theme', 'Change color theme']
    ];
    
    const table = new Table({
      head: [chalk.cyan('Command'), chalk.cyan('Description')],
      colWidths: [20, 60],
      style: {
        head: ['cyan'],
        border: ['gray']
      }
    });
    
    commands.forEach(([cmd, desc]) => {
      if (cmd) {
        table.push([chalk.yellow(cmd), desc]);
      } else {
        table.push(['', chalk.gray(desc)]);
      }
    });
    
    console.log('\n' + table.toString() + '\n');
  }

  // Show current status
  private showStatus(): void {
    const statusInfo = {
      'Current Directory': process.cwd(),
      'Aider Version': __version__,
      'Model': config.model,
      'API Base URL': config.apiBaseUrl,
      'API Timeout': config.apiTimeout + 'ms',
      'Theme': config.theme,
      'Session Active': currentSession ? 'Yes' : 'No',
      'Commands Run': sessionHistory.length
    };
    
    const table = new Table({
      head: [chalk.cyan('Setting'), chalk.cyan('Value')],
      colWidths: [20, 50],
      style: {
        head: ['cyan'],
        border: ['gray']
      }
    });
    
    Object.entries(statusInfo).forEach(([key, value]) => {
      table.push([chalk.yellow(key), String(value)]);
    });
    
    console.log('\n' + table.toString() + '\n');
  }

  // Handle commands
  private async handleCommand(input: string): Promise<void> {
    const command = input.trim();
    
    // Add to history
    sessionHistory.push({
      command,
      timestamp: new Date()
    });
    
    // Parse command
    if (command.startsWith('/')) {
      const [cmd, ...args] = command.slice(1).split(' ');
      
      switch (cmd.toLowerCase()) {
        case 'help':
          this.showHelp();
          break;
          
        case 'status':
          this.showStatus();
          break;
          
        case 'clear':
        case 'reset':
          console.clear();
          this.showWelcomeBanner();
          sessionHistory = [];
          break;
          
        case 'compact':
          await this.compactHistory(args.join(' '));
          break;
          
        case 'model':
          await this.changeModel();
          break;
          
        case 'theme':
          await this.changeTheme();
          break;
          
        case 'config':
          await this.showConfigPanel();
          break;
          
        case 'exit':
        case 'quit':
          console.log(chalk.gray('👋 Goodbye!'));
          process.exit(0);
          break;
          
        case 'doctor':
          await this.runDiagnostics();
          break;
          
        case 'cost':
          this.showSessionCost();
          break;
          
        case 'export':
          await this.exportConversation();
          break;
          
        case 'add-dir':
          await this.addWorkingDirectory();
          break;
          
        case 'agents':
          await this.manageAgents();
          break;
          
        case 'bug':
          this.submitFeedback();
          break;
          
        case 'hooks':
          await this.manageHooks();
          break;
          
        case 'ide':
          await this.manageIDE();
          break;
          
        case 'init':
          await this.initializeProject();
          break;
          
        case 'install-github-app':
          await this.installGitHubApp();
          break;
          
        case 'login':
          await this.loginAnthropic();
          break;
          
        case 'logout':
          await this.logoutAnthropic();
          break;
          
        case 'mcp':
          await this.manageMCP();
          break;
          
        case 'memory':
          await this.editMemory();
          break;
          
        case 'migrate-installer':
          await this.migrateInstaller();
          break;
          
        case 'permissions':
        case 'allowed-tools':
          await this.managePermissions();
          break;
          
        case 'pr-comments':
          await this.getPRComments(args.join(' '));
          break;
          
        case 'release-notes':
          this.showReleaseNotes();
          break;
          
        case 'resume':
          await this.resumeConversation();
          break;
          
        case 'review':
          await this.reviewPR(args.join(' '));
          break;
          
        case 'upgrade':
          this.showUpgradeInfo();
          break;
          
        case 'vim':
          await this.toggleVimMode();
          break;
          
        default:
          console.log(chalk.red(`Unknown command: /${cmd}`));
          console.log(chalk.gray('Type /help for available commands\n'));
      }
    } else {
      // Regular aider command or chat
      if (command.trim()) {
        console.log(chalk.cyan('Processing message: ') + command);
        console.log(chalk.yellow('🚧 AI chat functionality will be integrated here\n'));
        // TODO: Integrate with actual aider AI functionality
      }
    }
  }

  // Change AI model
  private async changeModel(): Promise<void> {
    const { model } = await inquirer.prompt([
      {
        type: 'list',
        name: 'model',
        message: 'Select AI model:',
        choices: [
          'gpt-4o',
          'gpt-4o-mini',
          'claude-3-5-sonnet',
          'claude-3-haiku',
          'gpt-3.5-turbo'
        ],
        default: config.model
      }
    ]);
    config.model = model;
    this.saveConfig();
    console.log(chalk.green(`✓ Model changed to ${model}\n`));
  }

  // Change color theme
  private async changeTheme(): Promise<void> {
    const { theme } = await inquirer.prompt([
      {
        type: 'list',
        name: 'theme',
        message: 'Select color theme:',
        choices: ['dark', 'light', 'auto'],
        default: config.theme
      }
    ]);
    config.theme = theme;
    this.saveConfig();
    console.log(chalk.green(`✓ Theme changed to ${theme}\n`));
  }

  // Show configuration panel
  private async showConfigPanel(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Configuration options:',
        choices: [
          'Change API timeout',
          'Change API base URL',
          'Reset to defaults',
          'Show current config',
          'Back to main'
        ]
      }
    ]);
    
    switch (action) {
      case 'Change API timeout':
        const { timeout } = await inquirer.prompt([
          {
            type: 'number',
            name: 'timeout',
            message: 'API timeout (ms):',
            default: config.apiTimeout
          }
        ]);
        config.apiTimeout = timeout;
        this.saveConfig();
        console.log(chalk.green(`✓ API timeout set to ${timeout}ms\n`));
        break;
        
      case 'Change API base URL':
        const { url } = await inquirer.prompt([
          {
            type: 'input',
            name: 'url',
            message: 'API base URL:',
            default: config.apiBaseUrl
          }
        ]);
        config.apiBaseUrl = url;
        this.saveConfig();
        console.log(chalk.green(`✓ API base URL set to ${url}\n`));
        break;
        
      case 'Show current config':
        this.showStatus();
        break;
    }
  }

  // Run diagnostics
  private async runDiagnostics(): Promise<void> {
    console.log(chalk.cyan('🔍 Running diagnostics...\n'));
    
    const checks = [
      {
        name: 'Node.js version',
        check: () => process.version,
        expected: 'v16+'
      },
      {
        name: 'Current directory access',
        check: () => {
          try {
            fs.accessSync(process.cwd(), fs.constants.R_OK | fs.constants.W_OK);
            return 'OK';
          } catch {
            return 'Failed';
          }
        },
        expected: 'OK'
      },
      {
        name: 'Configuration file',
        check: () => fs.existsSync(CONFIG_FILE) ? 'Found' : 'Not found',
        expected: 'Found'
      }
    ];
    
    const table = new Table({
      head: [chalk.cyan('Check'), chalk.cyan('Result'), chalk.cyan('Status')],
      colWidths: [25, 30, 15],
      style: {
        head: ['cyan'],
        border: ['gray']
      }
    });
    
    checks.forEach(({ name, check, expected }) => {
      try {
        const result = check();
        const status = result ? chalk.green('✓ PASS') : chalk.red('✗ FAIL');
        table.push([name, String(result), status]);
      } catch (error: any) {
        table.push([name, error.message, chalk.red('✗ FAIL')]);
      }
    });
    
    console.log(table.toString() + '\n');
  }

  // Show session cost (placeholder)
  private showSessionCost(): void {
    if (currentSession) {
      const duration = new Date().getTime() - currentSession.startTime;
      console.log(chalk.cyan(`Session duration: ${Math.floor(duration / 1000)}s`));
    } else {
      console.log(chalk.gray('No active session'));
    }
    console.log(chalk.gray('Cost tracking not implemented yet\n'));
  }

  // Compact conversation history
  private async compactHistory(instructions?: string): Promise<void> {
    console.log(chalk.cyan('🗜️ Compacting conversation history...'));
    if (instructions) {
      console.log(chalk.gray(`Instructions: ${instructions}`));
    }
    sessionHistory = sessionHistory.slice(-5); // Keep last 5 commands
    console.log(chalk.green('✓ Conversation history compacted\n'));
  }

  // Export conversation
  private async exportConversation(): Promise<void> {
    const { format } = await inquirer.prompt([
      {
        type: 'list',
        name: 'format',
        message: 'Export format:',
        choices: ['Markdown', 'JSON', 'Text', 'Clipboard']
      }
    ]);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `aider-conversation-${timestamp}`;
    
    console.log(chalk.cyan(`📤 Exporting conversation as ${format}...`));
    console.log(chalk.green(`✓ Conversation exported to ${filename}\n`));
  }

  // Add working directory
  private async addWorkingDirectory(): Promise<void> {
    const { directory } = await inquirer.prompt([
      {
        type: 'input',
        name: 'directory',
        message: 'Enter directory path:',
        default: process.cwd()
      }
    ]);
    
    if (fs.existsSync(directory)) {
      console.log(chalk.green(`✓ Added working directory: ${directory}\n`));
    } else {
      console.log(chalk.red(`✗ Directory not found: ${directory}\n`));
    }
  }

  // Manage agents
  private async manageAgents(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Agent management:',
        choices: [
          'List available agents',
          'Configure agent settings',
          'Enable/disable agents',
          'Back to main'
        ]
      }
    ]);
    
    switch (action) {
      case 'List available agents':
        console.log(chalk.cyan('🤖 Available agents:'));
        console.log('• Programming Agent - Code generation and debugging');
        console.log('• Review Agent - Code review and suggestions');
        console.log('• Documentation Agent - Generate documentation\n');
        break;
      default:
        console.log(chalk.yellow('🚧 Agent management features coming soon\n'));
    }
  }

  // Submit feedback
  private submitFeedback(): void {
    console.log(chalk.cyan('🐛 Submit feedback about Aider:'));
    console.log(chalk.gray('Visit: https://github.com/Aider-AI/aider-ts/issues'));
    console.log(chalk.gray('Or email: <EMAIL>\n'));
  }

  // Manage hooks
  private async manageHooks(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Hook management:',
        choices: [
          'List active hooks',
          'Add new hook',
          'Configure hook events',
          'Back to main'
        ]
      }
    ]);
    
    console.log(chalk.yellow('🚧 Hook management features coming soon\n'));
  }

  // Manage IDE integrations
  private async manageIDE(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'IDE integration:',
        choices: [
          'Show IDE status',
          'Configure VS Code integration',
          'Configure Vim integration',
          'Back to main'
        ]
      }
    ]);
    
    switch (action) {
      case 'Show IDE status':
        console.log(chalk.cyan('💻 IDE Integration Status:'));
        console.log('• VS Code: Not configured');
        console.log('• Vim: Not configured');
        console.log('• Terminal: Active\n');
        break;
      default:
        console.log(chalk.yellow('🚧 IDE integration features coming soon\n'));
    }
  }

  // Initialize project
  private async initializeProject(): Promise<void> {
    console.log(chalk.cyan('📝 Initializing CLAUDE.md file...'));
    
    const claudeContent = `# ${path.basename(process.cwd())}

## Project Overview
This project uses Aider for AI-powered pair programming.

## Getting Started
1. Set your API key: \`export OPENAI_API_KEY=your_key\`
2. Run aider: \`npm run dev\`
3. Start coding with AI assistance!

## Commands
Use \`/help\` to see all available commands.
`;
    
    try {
      fs.writeFileSync('CLAUDE.md', claudeContent);
      console.log(chalk.green('✓ CLAUDE.md file created successfully\n'));
    } catch (error) {
      console.log(chalk.red('✗ Failed to create CLAUDE.md file\n'));
    }
  }

  // Install GitHub App
  private async installGitHubApp(): Promise<void> {
    console.log(chalk.cyan('🔗 Setting up Claude GitHub Actions...'));
    console.log(chalk.gray('Visit: https://github.com/apps/claude-code'));
    console.log(chalk.yellow('🚧 GitHub App installation features coming soon\n'));
  }

  // Login to Anthropic
  private async loginAnthropic(): Promise<void> {
    console.log(chalk.cyan('🔐 Sign in with your Anthropic account...'));
    console.log(chalk.gray('Visit: https://console.anthropic.com'));
    console.log(chalk.yellow('🚧 Anthropic login features coming soon\n'));
  }

  // Logout from Anthropic
  private async logoutAnthropic(): Promise<void> {
    console.log(chalk.cyan('👋 Signing out from Anthropic account...'));
    console.log(chalk.green('✓ Successfully signed out\n'));
  }

  // Manage MCP servers
  private async manageMCP(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'MCP server management:',
        choices: [
          'List MCP servers',
          'Add MCP server',
          'Configure MCP settings',
          'Back to main'
        ]
      }
    ]);
    
    console.log(chalk.yellow('🚧 MCP server management features coming soon\n'));
  }

  // Edit memory files
  private async editMemory(): Promise<void> {
    console.log(chalk.cyan('🧠 Claude memory file editor...'));
    console.log(chalk.yellow('🚧 Memory editing features coming soon\n'));
  }

  // Migrate installer
  private async migrateInstaller(): Promise<void> {
    console.log(chalk.cyan('📦 Migrating from global to local installation...'));
    console.log(chalk.yellow('🚧 Migration features coming soon\n'));
  }

  // Manage permissions
  private async managePermissions(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Permission management:',
        choices: [
          'Show current permissions',
          'Add allowed tool',
          'Add denied tool',
          'Reset permissions',
          'Back to main'
        ]
      }
    ]);
    
    switch (action) {
      case 'Show current permissions':
        console.log(chalk.cyan('🔒 Current tool permissions:'));
        console.log('• File operations: Allowed');
        console.log('• Terminal commands: Allowed');
        console.log('• Network requests: Denied\n');
        break;
      default:
        console.log(chalk.yellow('🚧 Permission management features coming soon\n'));
    }
  }

  // Get PR comments
  private async getPRComments(prUrl: string): Promise<void> {
    if (!prUrl) {
      const { url } = await inquirer.prompt([
        {
          type: 'input',
          name: 'url',
          message: 'Enter GitHub PR URL:'
        }
      ]);
      prUrl = url;
    }
    
    console.log(chalk.cyan(`📝 Getting comments from PR: ${prUrl}`));
    console.log(chalk.yellow('🚧 PR comment fetching features coming soon\n'));
  }

  // Show release notes
  private showReleaseNotes(): void {
    console.log(chalk.cyan('📋 Aider Release Notes'));
    console.log(chalk.yellow('Version 0.85.3-dev'));
    console.log('• Enhanced Claude-like CLI interface');
    console.log('• Real-time command autocomplete');
    console.log('• Improved terminal UX');
    console.log('• Extended command set\n');
  }

  // Resume conversation
  private async resumeConversation(): Promise<void> {
    console.log(chalk.cyan('🔄 Resume conversation...'));
    console.log(chalk.yellow('🚧 Conversation resume features coming soon\n'));
  }

  // Review PR
  private async reviewPR(prUrl: string): Promise<void> {
    if (!prUrl) {
      const { url } = await inquirer.prompt([
        {
          type: 'input',
          name: 'url',
          message: 'Enter GitHub PR URL to review:'
        }
      ]);
      prUrl = url;
    }
    
    console.log(chalk.cyan(`🔍 Reviewing PR: ${prUrl}`));
    console.log(chalk.yellow('🚧 PR review features coming soon\n'));
  }

  // Show upgrade info
  private showUpgradeInfo(): void {
    console.log(chalk.cyan('⬆️ Upgrade to Claude Max'));
    console.log(chalk.gray('Get higher rate limits and more Opus access'));
    console.log(chalk.gray('Visit: https://console.anthropic.com/settings/plans\n'));
  }

  // Toggle Vim mode
  private async toggleVimMode(): Promise<void> {
    config.vimMode = !config.vimMode;
    this.saveConfig();
    const mode = config.vimMode ? 'Vim' : 'Normal';
    console.log(chalk.green(`✓ Switched to ${mode} editing mode\n`));
  }

  // Main CLI loop
  private async startInteractiveCLI(): Promise<void> {
    this.showWelcomeBanner();
    
    while (true) {
      try {
        const input = await this.getSmartInput();
        
        if (input && input.trim()) {
          await this.handleCommand(input);
        }
      } catch (error: any) {
        if (error.name === 'ExitPromptError') {
          console.log(chalk.gray('\n👋 Goodbye!'));
          break;
        }
        console.error(chalk.red(`Error: ${error.message}`));
      }
    }
  }

  private async handleMain(files: string[], options: any): Promise<void> {
    if (options.listModels) {
      this.showAvailableModels();
      return;
    }

    if (options.testModel) {
      await this.testModel(options.testModel);
      return;
    }

    // Check for API keys
    const apiKeys = this.checkApiKeys();
    if (apiKeys.length === 0) {
      this.showApiKeyHelp();
      return;
    }

    // If a single message is provided, process it and exit
    if (options.message) {
      console.log(chalk.cyan('Processing message: ') + options.message);
      console.log(chalk.yellow('🚧 Single message processing will be integrated here'));
      return;
    }

    // Start interactive CLI
    await this.startInteractiveCLI();
    // await main({ args: ... });
  }

  private async handleSetup(): Promise<void> {
    console.log(chalk.cyan.bold('\n🚀 Aider Setup Wizard\n'));
    
    console.log('Welcome to aider! Let\'s get you set up with AI pair programming.\n');
    
    console.log(chalk.yellow('Step 1: Get an API Key'));
    console.log('Choose one of these AI providers:\n');
    
    console.log(chalk.blue('🤖 OpenAI (Recommended)'));
    console.log('  • Models: GPT-4o, GPT-4o Mini, GPT-4 Turbo');
    console.log('  • Get key: https://platform.openai.com/api-keys');
    console.log('  • Set: export OPENAI_API_KEY=your_key_here\n');
    
    console.log(chalk.magenta('🧠 Anthropic'));
    console.log('  • Models: Claude 3.5 Sonnet, Claude 3 Haiku');
    console.log('  • Get key: https://console.anthropic.com/');
    console.log('  • Set: export ANTHROPIC_API_KEY=your_key_here\n');
    
    console.log(chalk.green('⚡ xAI'));
    console.log('  • Models: Grok Beta, Grok 3 Mini');
    console.log('  • Get key: https://console.x.ai/');
    console.log('  • Set: export XAI_API_KEY=your_key_here\n');
    
    console.log(chalk.yellow('Step 2: Run Aider'));
    console.log('Once you have an API key set up:');
    console.log(chalk.cyan('  aider                    ') + '# Start interactive session');
    console.log(chalk.cyan('  aider file1.js file2.py ') + '# Include specific files');
    console.log(chalk.cyan('  aider --model gpt-4o     ') + '# Use specific model');
    console.log(chalk.cyan('  aider --message "help"   ') + '# Send a message\n');
    
    console.log(chalk.green('✅ Setup complete! Set your API key and start coding with AI.'));
  }

  private handleConfig(): void {
    console.log(chalk.cyan.bold('\n⚙️  Aider Configuration\n'));
    
    console.log(chalk.yellow('Environment Variables:'));
    console.log('  OPENAI_API_KEY      - OpenAI API key');
    console.log('  ANTHROPIC_API_KEY   - Anthropic API key');
    console.log('  XAI_API_KEY         - xAI API key');
    console.log('  AIDER_MODEL         - Default model to use\n');
    
    console.log(chalk.yellow('Current Status:'));
    const apiKeys = this.checkApiKeys();
    if (apiKeys.length > 0) {
      console.log(chalk.green('✅ API keys found for: ' + apiKeys.join(', ')));
    } else {
      console.log(chalk.red('❌ No API keys found'));
    }
    
    console.log('\n' + chalk.yellow('Common Commands:'));
    console.log('  aider --help            # Show all options');
    console.log('  aider --list-models     # List available models');
    console.log('  aider --test-model gpt-4o # Test model connectivity');
    console.log('  npm run dev-old         # Run original aider (while CLI is in development)');
  }

  private checkApiKeys(): string[] {
    const keys = [];
    if (process.env.OPENAI_API_KEY) keys.push('OpenAI');
    if (process.env.ANTHROPIC_API_KEY) keys.push('Anthropic');
    if (process.env.XAI_API_KEY) keys.push('xAI');
    return keys;
  }

  private showApiKeyHelp(): void {
    console.log(chalk.red.bold('❌ No API keys found!\n'));
    
    console.log('You need to set an API key to use aider. Choose one:\n');
    
    console.log(chalk.blue('For OpenAI (Recommended):'));
    console.log('  export OPENAI_API_KEY=your_openai_key\n');
    
    console.log(chalk.magenta('For Anthropic:'));
    console.log('  export ANTHROPIC_API_KEY=your_anthropic_key\n');
    
    console.log(chalk.green('For xAI:'));
    console.log('  export XAI_API_KEY=your_xai_key\n');
    
    console.log(chalk.cyan('Run ') + chalk.bold('aider setup') + chalk.cyan(' for detailed instructions.'));
  }

  private showAvailableModels(): void {
    console.log(chalk.cyan.bold('\n🤖 Available Models\n'));
    
    console.log(chalk.blue('OpenAI Models:'));
    console.log('  gpt-4o              # Latest GPT-4 Omni (Recommended)');
    console.log('  gpt-4o-mini         # Fast and efficient');
    console.log('  gpt-4-turbo         # Previous generation');
    console.log('  gpt-3.5-turbo       # Budget option\n');
    
    console.log(chalk.magenta('Anthropic Models:'));
    console.log('  claude-3-5-sonnet-20241022  # Latest Claude (Recommended)');
    console.log('  claude-3-opus-20240229      # Most capable');
    console.log('  claude-3-sonnet-20240229    # Balanced');
    console.log('  claude-3-haiku-20240307     # Fast and efficient\n');
    
    console.log(chalk.green('xAI Models:'));
    console.log('  grok-beta           # Latest Grok');
    console.log('  grok-3              # Grok 3');
    console.log('  grok-3-mini         # Efficient Grok\n');
    
    console.log(chalk.yellow('Usage:'));
    console.log('  aider --model gpt-4o');
    console.log('  aider --model claude-3-5-sonnet-20241022');
    console.log('  aider --model grok-beta');
  }

  private async testModel(model: string): Promise<void> {
    console.log(chalk.yellow(`Testing ${model}...`));
    
    // Simple connectivity test
    const apiKeys = this.checkApiKeys();
    if (apiKeys.length === 0) {
      console.log(chalk.red('❌ No API keys found. Cannot test model.'));
      return;
    }
    
    console.log(chalk.green(`✅ API keys available. Model ${model} should work.`));
    console.log(chalk.gray('Note: Full model testing requires running an actual chat session.'));
  }

  private getDefaultModel(apiKeys: string[]): string {
    if (process.env.AIDER_MODEL) {
      return process.env.AIDER_MODEL;
    }
    
    if (apiKeys.includes('OpenAI')) {
      return 'gpt-4o';
    } else if (apiKeys.includes('Anthropic')) {
      return 'claude-3-5-sonnet-20241022';
    } else if (apiKeys.includes('xAI')) {
      return 'grok-beta';
    }
    
    return 'gpt-4o'; // fallback
  }

  private showSessionInfo(options: any, files: string[], apiKeys: string[]): void {
    const model = options.model || this.getDefaultModel(apiKeys);
    
    console.log(chalk.blue('┌─ Session Info ─────────────────────────────────────────┐'));
    console.log(chalk.blue('│') + ` Model: ${chalk.bold(model)}`.padEnd(55) + chalk.blue('│'));
    console.log(chalk.blue('│') + ` Files: ${files.length > 0 ? files.join(', ') : 'Auto-detect'}`.padEnd(55) + chalk.blue('│'));
    console.log(chalk.blue('│') + ` Providers: ${apiKeys.join(', ')}`.padEnd(55) + chalk.blue('│'));
    console.log(chalk.blue('└────────────────────────────────────────────────────────┘\n'));
    
    if (options.message) {
      console.log(chalk.yellow('Sending message: ') + options.message + '\n');
    }
  }

  public async run(): Promise<void> {
    try {
      await this.program.parseAsync();
    } catch (error) {
      console.error(chalk.red(`Error: ${error instanceof Error ? error.message : error}`));
      process.exit(1);
    }
  }
}

// CLI entry point
async function main() {
  const cli = new StandaloneCLI();
  await cli.run();
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.gray('\n👋 Goodbye!'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.gray('\n👋 Goodbye!'));
  process.exit(0);
});

if (require.main === module) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { main };
export default StandaloneCLI;