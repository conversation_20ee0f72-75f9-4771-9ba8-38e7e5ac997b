import { EventEmitter } from "events";
import * as fs from "fs";
import * as path from "path";
import { watch as chokidarWatch, FSWatcher } from "chokidar";
import * as _ from "lodash";
import { minimatch } from "minimatch";

export interface WatchOptions {
  ignored?: string[];
  ignoreInitial?: boolean;
  followSymlinks?: boolean;
  depth?: number;
  awaitWriteFinish?:
    | boolean
    | { stabilityThreshold?: number; pollInterval?: number };
  usePolling?: boolean;
  interval?: number;
  binaryInterval?: number;
  debounceDelay?: number;
  useGitIgnore?: boolean;
}

export interface FileChangeEvent {
  type: "add" | "change" | "unlink" | "addDir" | "unlinkDir";
  path: string;
  stats?: fs.Stats;
  timestamp: Date;
}

export interface WatcherStats {
  totalFiles: number;
  totalDirectories: number;
  eventsProcessed: number;
  lastActivity: Date | null;
  isActive: boolean;
}

export class FileWatcher extends EventEmitter {
  private watcher: FSWatcher | null = null;
  private watchedPaths: Set<string> = new Set();
  private options: WatchOptions;
  private isWatching: boolean = false;
  private stats: WatcherStats;
  private debouncedEmit: (event: FileChangeEvent) => void;
  private fileCache: Map<string, { mtime: number; size: number }> = new Map();

  private readonly defaultIgnorePatterns = [
    "**/node_modules/**",
    "**/.git/**",
    "**/dist/**",
    "**/build/**",
    "**/coverage/**",
    "**/*.log",
    "**/.DS_Store",
    "**/Thumbs.db",
    "**/*.tmp",
    "**/*.temp",
    "**/venv/**",
    "**/env/**",
    "**/__pycache__/**",
    "**/*.pyc",
    "**/.next/**",
    "**/.nuxt/**",
    "**/out/**",
    "**/.cache/**",
    "**/target/**",
    "**/vendor/**",
  ];

  constructor(options: WatchOptions = {}) {
    super();

    this.options = {
      ignored: [...this.defaultIgnorePatterns, ...(options.ignored || [])],
      ignoreInitial: options.ignoreInitial ?? true,
      followSymlinks: options.followSymlinks ?? false,
      depth: options.depth ?? 10,
      awaitWriteFinish: options.awaitWriteFinish ?? {
        stabilityThreshold: 100,
        pollInterval: 50,
      },
      usePolling: options.usePolling ?? false,
      interval: options.interval ?? 100,
      binaryInterval: options.binaryInterval ?? 300,
      debounceDelay: options.debounceDelay ?? 250,
      useGitIgnore: options.useGitIgnore ?? true,
    };

    this.stats = {
      totalFiles: 0,
      totalDirectories: 0,
      eventsProcessed: 0,
      lastActivity: null,
      isActive: false,
    };

    this.debouncedEmit = _.debounce(
      (event: FileChangeEvent) => this.emitChange(event),
      this.options.debounceDelay,
    );

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // Handle process termination
    process.on("SIGINT", () => this.stop());
    process.on("SIGTERM", () => this.stop());
    process.on("exit", () => this.stop());
  }

  public async start(paths: string | string[]): Promise<void> {
    if (this.isWatching) {
      console.warn("Watcher is already running");
      return;
    }

    const pathsArray = Array.isArray(paths) ? paths : [paths];

    // Validate paths
    for (const watchPath of pathsArray) {
      if (!fs.existsSync(watchPath)) {
        throw new Error(`Path does not exist: ${watchPath}`);
      }
      this.watchedPaths.add(path.resolve(watchPath));
    }

    try {
      await this.initializeWatcher(pathsArray);
      this.isWatching = true;
      this.stats.isActive = true;
      this.emit("ready");
      console.log(`File watcher started for ${pathsArray.length} path(s)`);
    } catch (error) {
      this.emit("error", error);
      throw error;
    }
  }

  private async initializeWatcher(paths: string[]): Promise<void> {
    const watchOptions = {
      ignored: this.buildIgnorePatterns(),
      ignoreInitial: this.options.ignoreInitial,
      followSymlinks: this.options.followSymlinks,
      depth: this.options.depth,
      awaitWriteFinish: this.options.awaitWriteFinish,
      usePolling: this.options.usePolling,
      interval: this.options.interval,
      binaryInterval: this.options.binaryInterval,
      persistent: true,
      alwaysStat: true,
    };

    this.watcher = chokidarWatch(paths, watchOptions);

    // Set up event handlers
    this.watcher
      .on("add", (filePath, stats) =>
        this.handleFileEvent("add", filePath, stats),
      )
      .on("change", (filePath, stats) =>
        this.handleFileEvent("change", filePath, stats),
      )
      .on("unlink", (filePath) => this.handleFileEvent("unlink", filePath))
      .on("addDir", (dirPath, stats) =>
        this.handleDirectoryEvent("addDir", dirPath, stats),
      )
      .on("unlinkDir", (dirPath) =>
        this.handleDirectoryEvent("unlinkDir", dirPath),
      )
      .on("error", (error) => this.handleError(error))
      .on("ready", () => this.handleReady());
  }

  private buildIgnorePatterns(): Array<string | ((path: string) => boolean)> {
    const patterns = [...this.options.ignored!];

    // Add git ignore patterns if enabled
    if (this.options.useGitIgnore) {
      patterns.push((filePath: string) => this.isGitIgnored(filePath));
    }

    return patterns;
  }

  private isGitIgnored(filePath: string): boolean {
    // Simple git ignore check - look for .gitignore files
    let currentDir = path.dirname(filePath);
    const rootDir = process.cwd();

    while (currentDir && currentDir !== rootDir) {
      const gitignorePath = path.join(currentDir, ".gitignore");

      if (fs.existsSync(gitignorePath)) {
        try {
          const gitignoreContent = fs.readFileSync(gitignorePath, "utf-8");
          const patterns = gitignoreContent
            .split("\n")
            .map((line) => line.trim())
            .filter((line) => line && !line.startsWith("#"));

          const relativePath = path.relative(currentDir, filePath);

          for (const pattern of patterns) {
            if (minimatch(relativePath, pattern, { dot: true })) {
              return true;
            }
          }
        } catch (error) {
          // Ignore errors reading .gitignore
        }
      }

      const parentDir = path.dirname(currentDir);
      if (parentDir === currentDir) break;
      currentDir = parentDir;
    }

    return false;
  }

  private handleFileEvent(
    type: "add" | "change" | "unlink",
    filePath: string,
    stats?: fs.Stats,
  ): void {
    const absolutePath = path.resolve(filePath);

    // Update cache for change detection
    if (type === "change" && stats) {
      const cached = this.fileCache.get(absolutePath);
      if (
        cached &&
        cached.mtime === stats.mtime.getTime() &&
        cached.size === stats.size
      ) {
        return; // No actual change
      }
    }

    if (stats) {
      this.fileCache.set(absolutePath, {
        mtime: stats.mtime.getTime(),
        size: stats.size,
      });
    } else if (type === "unlink") {
      this.fileCache.delete(absolutePath);
    }

    const event: FileChangeEvent = {
      type,
      path: absolutePath,
      stats,
      timestamp: new Date(),
    };

    this.stats.eventsProcessed++;
    this.stats.lastActivity = event.timestamp;

    if (type === "add") {
      this.stats.totalFiles++;
    } else if (type === "unlink") {
      this.stats.totalFiles = Math.max(0, this.stats.totalFiles - 1);
    }

    this.debouncedEmit(event);
  }

  private handleDirectoryEvent(
    type: "addDir" | "unlinkDir",
    dirPath: string,
    stats?: fs.Stats,
  ): void {
    const absolutePath = path.resolve(dirPath);

    const event: FileChangeEvent = {
      type,
      path: absolutePath,
      stats,
      timestamp: new Date(),
    };

    this.stats.eventsProcessed++;
    this.stats.lastActivity = event.timestamp;

    if (type === "addDir") {
      this.stats.totalDirectories++;
    } else if (type === "unlinkDir") {
      this.stats.totalDirectories = Math.max(
        0,
        this.stats.totalDirectories - 1,
      );
    }

    this.debouncedEmit(event);
  }

  private emitChange(event: FileChangeEvent): void {
    this.emit("change", event);
    this.emit(event.type, event.path, event.stats);
  }

  private handleError(error: Error): void {
    console.error("File watcher error:", error);
    this.emit("error", error);
  }

  private handleReady(): void {
    console.log("File watcher is ready and monitoring for changes");
  }

  public pause(): void {
    if (this.watcher && this.isWatching) {
      this.watcher.unwatch("*");
      this.stats.isActive = false;
      this.emit("paused");
      console.log("File watcher paused");
    }
  }

  public async resume(): Promise<void> {
    if (this.watcher && !this.stats.isActive) {
      const paths = Array.from(this.watchedPaths);
      this.watcher.add(paths);
      this.stats.isActive = true;
      this.emit("resumed");
      console.log("File watcher resumed");
    }
  }

  public stop(): void {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
    }

    this.isWatching = false;
    this.stats.isActive = false;
    this.watchedPaths.clear();
    this.fileCache.clear();
    this.emit("stopped");
    console.log("File watcher stopped");
  }

  public addPath(watchPath: string): void {
    if (!this.watcher) {
      throw new Error("Watcher is not initialized");
    }

    const absolutePath = path.resolve(watchPath);

    if (!fs.existsSync(absolutePath)) {
      throw new Error(`Path does not exist: ${absolutePath}`);
    }

    this.watcher.add(absolutePath);
    this.watchedPaths.add(absolutePath);
    this.emit("pathAdded", absolutePath);
  }

  public removePath(watchPath: string): void {
    if (!this.watcher) {
      throw new Error("Watcher is not initialized");
    }

    const absolutePath = path.resolve(watchPath);
    this.watcher.unwatch(absolutePath);
    this.watchedPaths.delete(absolutePath);
    this.emit("pathRemoved", absolutePath);
  }

  public getWatchedPaths(): string[] {
    return Array.from(this.watchedPaths);
  }

  public getStats(): WatcherStats {
    return { ...this.stats };
  }

  public isActive(): boolean {
    return this.isWatching && this.stats.isActive;
  }

  public getWatchedFiles(): string[] {
    if (!this.watcher) return [];

    const watchedFiles = this.watcher.getWatched();
    const files: string[] = [];

    for (const [dir, fileNames] of Object.entries(watchedFiles)) {
      for (const fileName of fileNames) {
        files.push(path.join(dir, fileName));
      }
    }

    return files;
  }

  public isPathWatched(filePath: string): boolean {
    const absolutePath = path.resolve(filePath);

    for (const watchedPath of this.watchedPaths) {
      if (absolutePath.startsWith(watchedPath)) {
        return true;
      }
    }

    return false;
  }

  public clearCache(): void {
    this.fileCache.clear();
    this.emit("cacheCleared");
  }
}

// Utility functions
export function createWatcher(
  paths: string | string[],
  options?: WatchOptions,
): FileWatcher {
  const watcher = new FileWatcher(options);
  watcher.start(paths).catch((error) => {
    console.error("Failed to start file watcher:", error);
  });
  return watcher;
}

export function watchFiles(
  paths: string | string[],
  callback: (event: FileChangeEvent) => void,
  options?: WatchOptions,
): FileWatcher {
  const watcher = new FileWatcher(options);
  watcher.on("change", callback);
  watcher.start(paths).catch((error) => {
    console.error("Failed to start file watcher:", error);
  });
  return watcher;
}

export function watchDirectory(
  directory: string,
  extensions: string[] = [],
  callback: (event: FileChangeEvent) => void,
  options?: WatchOptions,
): FileWatcher {
  const watcherOptions: WatchOptions = {
    ...options,
    ignored: [
      ...(options?.ignored || []),
      ...(extensions.length > 0
        ? [`**/*!(${extensions.map((ext) => `*${ext}`).join("|")})`]
        : []),
    ],
  };

  return watchFiles(directory, callback, watcherOptions);
}

export default FileWatcher;
