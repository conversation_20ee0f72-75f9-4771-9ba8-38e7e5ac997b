import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';

export interface VoiceOptions {
  enabled?: boolean;
  sttProvider?: 'browser' | 'openai' | 'google' | 'azure';
  ttsProvider?: 'browser' | 'openai' | 'google' | 'azure' | 'elevenlabs';
  language?: string;
  voice?: string;
  speed?: number;
  pitch?: number;
  volume?: number;
  autoListen?: boolean;
  hotwords?: string[];
  wakeWord?: string;
}

export interface VoiceRecognitionResult {
  text: string;
  confidence: number;
  isFinal: boolean;
  alternatives?: Array<{
    text: string;
    confidence: number;
  }>;
}

export interface VoiceSynthesisOptions {
  text: string;
  voice?: string;
  speed?: number;
  pitch?: number;
  volume?: number;
  outputPath?: string;
}

export interface AudioBuffer {
  data: Buffer;
  sampleRate: number;
  channels: number;
  duration: number;
}

export class VoiceManager extends EventEmitter {
  private options: VoiceOptions;
  private isListening: boolean = false;
  private isSpeaking: boolean = false;
  private recognition?: any; // SpeechRecognition
  private synthesis?: any; // SpeechSynthesis
  private currentUtterance?: any;
  private audioContext?: AudioContext;
  private mediaStream?: MediaStream;
  private recorder?: MediaRecorder;
  private recordedChunks: BlobPart[] = [];

  constructor(options: VoiceOptions = {}) {
    super();

    this.options = {
      enabled: options.enabled ?? false,
      sttProvider: options.sttProvider || 'browser',
      ttsProvider: options.ttsProvider || 'browser',
      language: options.language || 'en-US',
      voice: options.voice,
      speed: options.speed ?? 1.0,
      pitch: options.pitch ?? 1.0,
      volume: options.volume ?? 1.0,
      autoListen: options.autoListen ?? false,
      hotwords: options.hotwords || ['aider', 'hey aider'],
      wakeWord: options.wakeWord || 'aider',
    };

    this.initializeVoiceServices();
  }

  private initializeVoiceServices(): void {
    if (!this.options.enabled) return;

    try {
      // Initialize browser-based speech recognition
      if (this.options.sttProvider === 'browser' && typeof window !== 'undefined') {
        this.initializeBrowserSTT();
      }

      // Initialize browser-based speech synthesis
      if (this.options.ttsProvider === 'browser' && typeof window !== 'undefined') {
        this.initializeBrowserTTS();
      }

      // Initialize external providers
      this.initializeExternalProviders();

    } catch (error) {
      console.error('Failed to initialize voice services:', error);
      this.emit('error', error);
    }
  }

  private initializeBrowserSTT(): void {
    if (typeof window === 'undefined') return;

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      console.warn('Speech Recognition not supported in this browser');
      return;
    }

    this.recognition = new SpeechRecognition();
    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.lang = this.options.language;

    this.recognition.onresult = (event: any) => {
      const results: VoiceRecognitionResult[] = [];

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const alternatives = Array.from(result).map((alt: any) => ({
          text: alt.transcript,
          confidence: alt.confidence,
        }));

        results.push({
          text: result[0].transcript,
          confidence: result[0].confidence,
          isFinal: result.isFinal,
          alternatives,
        });
      }

      this.emit('speechResult', results);
    };

    this.recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error);
      this.emit('error', new Error(`Speech recognition error: ${event.error}`));
    };

    this.recognition.onend = () => {
      this.isListening = false;
      this.emit('listeningEnd');

      if (this.options.autoListen) {
        setTimeout(() => this.startListening(), 100);
      }
    };
  }

  private initializeBrowserTTS(): void {
    if (typeof window === 'undefined') return;

    if (!window.speechSynthesis) {
      console.warn('Speech Synthesis not supported in this browser');
      return;
    }

    this.synthesis = window.speechSynthesis;
  }

  private initializeExternalProviders(): void {
    // Placeholder for external provider initialization
    // Would integrate with OpenAI, Google Cloud, Azure, etc.

    switch (this.options.sttProvider) {
      case 'openai':
        this.initializeOpenAISTT();
        break;
      case 'google':
        this.initializeGoogleSTT();
        break;
      case 'azure':
        this.initializeAzureSTT();
        break;
    }

    switch (this.options.ttsProvider) {
      case 'openai':
        this.initializeOpenAITTS();
        break;
      case 'google':
        this.initializeGoogleTTS();
        break;
      case 'elevenlabs':
        this.initializeElevenLabsTTS();
        break;
    }
  }

  private initializeOpenAISTT(): void {
    // Placeholder for OpenAI Whisper integration
    console.log('OpenAI STT provider initialized');
  }

  private initializeGoogleSTT(): void {
    // Placeholder for Google Cloud Speech-to-Text
    console.log('Google STT provider initialized');
  }

  private initializeAzureSTT(): void {
    // Placeholder for Azure Speech Services
    console.log('Azure STT provider initialized');
  }

  private initializeOpenAITTS(): void {
    // Placeholder for OpenAI TTS
    console.log('OpenAI TTS provider initialized');
  }

  private initializeGoogleTTS(): void {
    // Placeholder for Google Cloud Text-to-Speech
    console.log('Google TTS provider initialized');
  }

  private initializeElevenLabsTTS(): void {
    // Placeholder for ElevenLabs integration
    console.log('ElevenLabs TTS provider initialized');
  }

  public async startListening(): Promise<void> {
    if (!this.options.enabled || this.isListening) return;

    try {
      if (this.recognition) {
        this.recognition.start();
        this.isListening = true;
        this.emit('listeningStart');
      } else {
        // Fallback to manual recording
        await this.startRecording();
      }
    } catch (error) {
      console.error('Failed to start listening:', error);
      this.emit('error', error);
    }
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if (this.recognition) {
      this.recognition.stop();
    }

    if (this.recorder && this.recorder.state === 'recording') {
      this.recorder.stop();
    }

    this.isListening = false;
    this.emit('listeningEnd');
  }

  private async startRecording(): Promise<void> {
    try {
      this.mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.recorder = new MediaRecorder(this.mediaStream);
      this.recordedChunks = [];

      this.recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.recorder.onstop = async () => {
        const audioBlob = new Blob(this.recordedChunks, { type: 'audio/wav' });
        await this.processAudioBlob(audioBlob);
      };

      this.recorder.start();
      this.isListening = true;
      this.emit('listeningStart');
    } catch (error) {
      console.error('Failed to start recording:', error);
      this.emit('error', error);
    }
  }

  private async processAudioBlob(audioBlob: Blob): Promise<void> {
    // Convert blob to buffer and process with external STT service
    const arrayBuffer = await audioBlob.arrayBuffer();
    const audioBuffer = Buffer.from(arrayBuffer);

    try {
      const result = await this.transcribeAudio(audioBuffer);
      this.emit('speechResult', [result]);
    } catch (error) {
      console.error('Failed to process audio:', error);
      this.emit('error', error);
    }
  }

  private async transcribeAudio(audioBuffer: Buffer): Promise<VoiceRecognitionResult> {
    // Placeholder for external STT processing
    // Would send to OpenAI Whisper, Google Cloud, etc.

    return {
      text: 'Transcription placeholder',
      confidence: 0.9,
      isFinal: true,
    };
  }

  public async speak(text: string, options: Partial<VoiceSynthesisOptions> = {}): Promise<void> {
    if (!this.options.enabled || this.isSpeaking) return;

    const synthOptions: VoiceSynthesisOptions = {
      text,
      voice: options.voice || this.options.voice,
      speed: options.speed ?? this.options.speed,
      pitch: options.pitch ?? this.options.pitch,
      volume: options.volume ?? this.options.volume,
      ...options,
    };

    try {
      if (this.options.ttsProvider === 'browser' && this.synthesis) {
        await this.speakWithBrowser(synthOptions);
      } else {
        await this.speakWithExternalProvider(synthOptions);
      }
    } catch (error) {
      console.error('Failed to speak text:', error);
      this.emit('error', error);
    }
  }

  private async speakWithBrowser(options: VoiceSynthesisOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.synthesis) {
        reject(new Error('Speech synthesis not available'));
        return;
      }

      const utterance = new SpeechSynthesisUtterance(options.text);
      utterance.rate = options.speed!;
      utterance.pitch = options.pitch!;
      utterance.volume = options.volume!;

      if (options.voice) {
        const voices = this.synthesis.getVoices();
        const selectedVoice = voices.find((voice: any) =>
          voice.name === options.voice || voice.lang.includes(this.options.language!)
        );
        if (selectedVoice) {
          utterance.voice = selectedVoice;
        }
      }

      utterance.onstart = () => {
        this.isSpeaking = true;
        this.emit('speakingStart');
      };

      utterance.onend = () => {
        this.isSpeaking = false;
        this.emit('speakingEnd');
        resolve();
      };

      utterance.onerror = (event) => {
        this.isSpeaking = false;
        this.emit('error', new Error(`Speech synthesis error: ${event.error}`));
        reject(new Error(`Speech synthesis error: ${event.error}`));
      };

      this.currentUtterance = utterance;
      this.synthesis.speak(utterance);
    });
  }

  private async speakWithExternalProvider(options: VoiceSynthesisOptions): Promise<void> {
    // Placeholder for external TTS providers
    console.log(`Speaking with ${this.options.ttsProvider}: ${options.text}`);

    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 1000));

    this.emit('speakingStart');
    this.isSpeaking = true;

    // Simulate speaking duration
    setTimeout(() => {
      this.isSpeaking = false;
      this.emit('speakingEnd');
    }, options.text.length * 50); // Rough estimate
  }

  public stopSpeaking(): void {
    if (!this.isSpeaking) return;

    if (this.synthesis && this.currentUtterance) {
      this.synthesis.cancel();
      this.currentUtterance = null;
    }

    this.isSpeaking = false;
    this.emit('speakingEnd');
  }

  public getAvailableVoices(): Array<{ name: string; lang: string; gender?: string }> {
    if (this.synthesis) {
      return this.synthesis.getVoices().map((voice: any) => ({
        name: voice.name,
        lang: voice.lang,
        gender: voice.name.toLowerCase().includes('female') ? 'female' : 'male',
      }));
    }

    return [];
  }

  public isVoiceEnabled(): boolean {
    return this.options.enabled;
  }

  public isCurrentlyListening(): boolean {
    return this.isListening;
  }

  public isCurrentlySpeaking(): boolean {
    return this.isSpeaking;
  }

  public updateOptions(newOptions: Partial<VoiceOptions>): void {
    this.options = { ...this.options, ...newOptions };

    if (newOptions.enabled !== undefined) {
      if (newOptions.enabled) {
        this.initializeVoiceServices();
      } else {
        this.disable();
      }
    }
  }

  public disable(): void {
    this.stopListening();
    this.stopSpeaking();

    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
    }

    this.options.enabled = false;
  }

  public async saveAudioToFile(audioData: Buffer, filePath: string): Promise<void> {
    try {
      fs.writeFileSync(filePath, audioData);
      console.log(`Audio saved to: ${filePath}`);
    } catch (error) {
      console.error(`Failed to save audio to ${filePath}:`, error);
      throw error;
    }
  }

  public detectWakeWord(text: string): boolean {
    const lowerText = text.toLowerCase();
    return this.options.hotwords?.some(word => lowerText.includes(word.toLowerCase())) || false;
  }
}

// Utility functions
export function createVoiceManager(options?: VoiceOptions): VoiceManager {
  return new VoiceManager(options);
}

export function isVoiceSupported(): boolean {
  if (typeof window === 'undefined') return false;

  const hasSTT = !!(window as any).SpeechRecognition || !!(window as any).webkitSpeechRecognition;
  const hasTTS = !!window.speechSynthesis;

  return hasSTT || hasTTS;
}

export function getVoiceCapabilities(): { stt: boolean; tts: boolean; mediaDevices: boolean } {
  if (typeof window === 'undefined') {
    return { stt: false, tts: false, mediaDevices: false };
  }

  return {
    stt: !!(window as any).SpeechRecognition || !!(window as any).webkitSpeechRecognition,
    tts: !!window.speechSynthesis,
    mediaDevices: !!navigator.mediaDevices?.getUserMedia,
  };
}

export default VoiceManager;
