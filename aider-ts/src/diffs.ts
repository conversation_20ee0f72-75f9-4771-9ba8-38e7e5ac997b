import * as fs from 'fs';
import * as path from 'path';
import { dump } from './dump';

/**
 * Create a progress bar string for the given percentage
 */
export function createProgressBar(percentage: number): string {
  const block = "█";
  const empty = "░";
  const totalBlocks = 30;
  const filledBlocks = Math.floor(totalBlocks * percentage / 100);
  const emptyBlocks = totalBlocks - filledBlocks;
  return block.repeat(filledBlocks) + empty.repeat(emptyBlocks);
}

/**
 * Assert that lines end with newlines (except possibly the last one)
 */
export function assertNewlines(lines: string[]): void {
  if (!lines || lines.length === 0) {
    return;
  }
  for (let i = 0; i < lines.length - 1; i++) {
    const line = lines[i];
    if (!line || !line.endsWith('\n')) {
      throw new Error(`Line ${i} does not end with newline: ${line}`);
    }
  }
}

/**
 * Given only the first part of an updated file, show the diff while
 * ignoring the block of "deleted" lines that are past the end of the
 * partially complete update.
 */
export function diffPartialUpdate(
  linesOrig: string[],
  linesUpdated: string[],
  final = false,
  fname?: string
): string {
  assertNewlines(linesOrig);

  const numOrigLines = linesOrig.length;

  let lastNonDeleted: number | null;
  if (final) {
    lastNonDeleted = numOrigLines;
  } else {
    lastNonDeleted = findLastNonDeleted(linesOrig, linesUpdated);
  }

  if (lastNonDeleted === null) {
    return "";
  }

  let pct: number;
  if (numOrigLines > 0) {
    pct = (lastNonDeleted * 100) / numOrigLines;
  } else {
    pct = 50;
  }

  const bar = createProgressBar(pct);
  const progressLine = ` ${lastNonDeleted.toString().padStart(3)} / ${numOrigLines.toString().padStart(3)} lines [${bar}] ${pct.toFixed(0).padStart(3)}%\n`;

  const truncatedOrig = linesOrig.slice(0, lastNonDeleted);
  let modifiedUpdated = [...linesUpdated];

  if (!final) {
    // Replace the last line with the progress bar
    modifiedUpdated = [...linesUpdated.slice(0, -1), progressLine];
  }

  // Create unified diff
  const diff = createUnifiedDiff(truncatedOrig, modifiedUpdated);

  if (!diff) {
    return "";
  }

  // Find appropriate backticks that don't conflict with content
  let backticks = "```";
  for (let i = 3; i < 10; i++) {
    backticks = "`".repeat(i);
    if (!diff.includes(backticks)) {
      break;
    }
  }

  let show = `${backticks}diff\n`;
  if (fname) {
    show += `--- ${fname} original\n`;
    show += `+++ ${fname} updated\n`;
  }

  show += diff;
  show += `${backticks}\n\n`;

  return show;
}

/**
 * Find the last non-deleted line number in the original file
 */
export function findLastNonDeleted(linesOrig: string[], linesUpdated: string[]): number | null {
  const diff = createNdiff(linesOrig, linesUpdated);

  let numOrig = 0;
  let lastNonDeletedOrig: number | null = null;

  for (const line of diff) {
    if (line.length === 0) continue;

    const code = line[0];
    if (code === ' ') {
      // Unchanged line
      numOrig += 1;
      lastNonDeletedOrig = numOrig;
    } else if (code === '-') {
      // Line only in original
      numOrig += 1;
    } else if (code === '+') {
      // Line only in updated - don't increment numOrig
      continue;
    }
  }

  return lastNonDeletedOrig;
}

/**
 * Create a simple unified diff between two sets of lines
 */
function createUnifiedDiff(linesA: string[], linesB: string[]): string {
  // Simple implementation - for production, you'd want a more sophisticated diff algorithm
  const diff: string[] = [];

  const maxLen = Math.max(linesA.length, linesB.length);

  for (let i = 0; i < maxLen; i++) {
    const lineA = i < linesA.length ? linesA[i] : undefined;
    const lineB = i < linesB.length ? linesB[i] : undefined;

    if (lineA === undefined) {
      // Line only in B
      diff.push(`+${lineB}`);
    } else if (lineB === undefined) {
      // Line only in A
      diff.push(`-${lineA}`);
    } else if (lineA !== lineB) {
      // Lines differ
      diff.push(`-${lineA}`);
      diff.push(`+${lineB}`);
    } else {
      // Lines are the same
      diff.push(` ${lineA}`);
    }
  }

  return diff.join('');
}

/**
 * Create an ndiff between two sets of lines (simplified version)
 */
function createNdiff(linesA: string[], linesB: string[]): string[] {
  const result: string[] = [];
  let i = 0, j = 0;

  while (i < linesA.length || j < linesB.length) {
    if (i >= linesA.length) {
      // Remaining lines are additions
      result.push(`+${linesB[j]}`);
      j++;
    } else if (j >= linesB.length) {
      // Remaining lines are deletions
      result.push(`-${linesA[i]}`);
      i++;
    } else if (linesA[i] === linesB[j]) {
      // Lines match
      result.push(` ${linesA[i]}`);
      i++;
      j++;
    } else {
      // Lines differ - this is simplified, real ndiff is more complex
      // For now, assume it's a replacement
      result.push(`-${linesA[i]}`);
      result.push(`+${linesB[j]}`);
      i++;
      j++;
    }
  }

  return result;
}

/**
 * Main function for command line usage
 */
export async function main(): Promise<void> {
  const args = process.argv.slice(2);

  if (args.length !== 2) {
    console.log("Usage: node diffs.js file1 file2");
    process.exit(1);
  }

  const [fileOrig, fileUpdated] = args;

  try {
    const linesOrig = fs.readFileSync(fileOrig, 'utf-8').split('\n').map(line => line + '\n');
    const allLinesUpdated = fs.readFileSync(fileUpdated, 'utf-8').split('\n').map(line => line + '\n');

    for (let i = 6; i <= allLinesUpdated.length; i += 5) {
      const linesUpdated = allLinesUpdated.slice(0, i);
      const res = diffPartialUpdate(linesOrig, linesUpdated, false, fileUpdated);
      console.log(res);

      // Wait for user input (simplified - in real implementation you'd use readline)
      await new Promise(resolve => {
        process.stdin.once('data', () => resolve(undefined));
      });
    }
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Export for module use
export default {
  createProgressBar,
  assertNewlines,
  diffPartialUpdate,
  findLastNonDeleted,
  main
};

// CLI entry point
if (require.main === module) {
  main().catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
}
