import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

export const DEFAULT_MODEL_NAME = 'gpt-4o';
export const ANTHROPIC_BETA_HEADER = 'prompt-caching-2024-07-31,pdfs-2024-09-25';
export const RETRY_TIMEOUT = 60;
export const REQUEST_TIMEOUT = 600;

export const OPENAI_MODELS = [
  'o1',
  'o1-preview',
  'o1-mini',
  'o3-mini',
  'gpt-4',
  'gpt-4o',
  'gpt-4o-2024-05-13',
  'gpt-4-turbo-preview',
  'gpt-4-0314',
  'gpt-4-0613',
  'gpt-4-32k',
  'gpt-4-32k-0314',
  'gpt-4-32k-0613',
  'gpt-4-turbo',
  'gpt-4-turbo-2024-04-09',
  'gpt-4-1106-preview',
  'gpt-4-0125-preview',
  'gpt-4-vision-preview',
  'gpt-4-1106-vision-preview',
  'gpt-4o-mini',
  'gpt-4o-mini-2024-07-18',
  'gpt-3.5-turbo',
  'gpt-3.5-turbo-0301',
  'gpt-3.5-turbo-0613',
  'gpt-3.5-turbo-1106',
  'gpt-3.5-turbo-0125',
  'gpt-3.5-turbo-16k',
  'gpt-3.5-turbo-16k-0613',
];

export const ANTHROPIC_MODELS = [
  'claude-2',
  'claude-2.1',
  'claude-3-haiku-20240307',
  'claude-3-5-haiku-20241022',
  'claude-3-opus-20240229',
  'claude-3-sonnet-20240229',
  'claude-3-5-sonnet-20240620',
  'claude-3-5-sonnet-20241022',
  'claude-sonnet-4-20250514',
  'claude-opus-4-20250514',
];

export const MODEL_ALIASES: Record<string, string> = {
  // Claude models
  'sonnet': 'anthropic/claude-sonnet-4-20250514',
  'haiku': 'claude-3-5-haiku-20241022',
  'opus': 'claude-opus-4-20250514',
  // GPT models
  '4': 'gpt-4-0613',
  '4o': 'gpt-4o',
  '4-turbo': 'gpt-4-1106-preview',
  '35turbo': 'gpt-3.5-turbo',
  '35-turbo': 'gpt-3.5-turbo',
  '3': 'gpt-3.5-turbo',
  // Other models
  'deepseek': 'deepseek/deepseek-chat',
  'flash': 'gemini/gemini-2.5-flash',
  'quasar': 'openrouter/openrouter/quasar-alpha',
  'r1': 'deepseek/deepseek-reasoner',
  'gemini-2.5-pro': 'gemini/gemini-2.5-pro',
  'gemini': 'gemini/gemini-2.5-pro',
  'gemini-exp': 'gemini/gemini-2.5-pro-exp-03-25',
  'grok3': 'xai/grok-3-beta',
  'optimus': 'openrouter/openrouter/optimus-alpha',
};

export interface ModelSettings {
  name: string;
  editFormat?: string;
  weakModelName?: string;
  useRepoMap?: boolean;
  sendUndoReply?: boolean;
  lazy?: boolean;
  overeager?: boolean;
  reminder?: string;
  examplesAsSysMsg?: boolean;
  extraParams?: Record<string, any>;
  cacheControl?: boolean;
  cachesByDefault?: boolean;
  useSystemPrompt?: boolean;
  useTemperature?: boolean | number;
  streaming?: boolean;
  editorModelName?: string;
  editorEditFormat?: string;
  reasoningTag?: string;
  removeReasoning?: string; // Deprecated alias for reasoningTag
  systemPromptPrefix?: string;
  acceptsSettings?: string[];
}

export interface ModelInfo {
  maxInputTokens?: number;
  maxOutputTokens?: number;
  inputCostPer1kTokens?: number;
  outputCostPer1kTokens?: number;
  litellmProvider?: string;
  mode?: string;
  supportsVision?: boolean;
  supportsToolCalls?: boolean;
}

export interface ValidationResult {
  missingKeys?: string[];
  keysInEnvironment?: string[];
}

export class ModelInfoManager {
  private static readonly MODEL_INFO_URL =
    'https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json';
  private static readonly CACHE_TTL = 60 * 60 * 24; // 24 hours

  private cacheDir: string;
  private cacheFile: string;
  private content: Record<string, ModelInfo> | null = null;
  private localModelMetadata: Record<string, ModelInfo> = {};
  private verifySSL: boolean = true;
  private cacheLoaded: boolean = false;

  constructor() {
    this.cacheDir = path.join(os.homedir(), '.aider', 'caches');
    this.cacheFile = path.join(this.cacheDir, 'model_prices_and_context_window.json');
  }

  public setVerifySSL(verifySSL: boolean): void {
    this.verifySSL = verifySSL;
  }

  private loadCache(): void {
    if (this.cacheLoaded) return;

    try {
      if (!fs.existsSync(this.cacheDir)) {
        fs.mkdirSync(this.cacheDir, { recursive: true });
      }

      if (fs.existsSync(this.cacheFile)) {
        const stats = fs.statSync(this.cacheFile);
        const cacheAge = (Date.now() - stats.mtime.getTime()) / 1000;

        if (cacheAge < ModelInfoManager.CACHE_TTL) {
          try {
            const content = fs.readFileSync(this.cacheFile, 'utf-8');
            this.content = JSON.parse(content);
          } catch (error) {
            // If cache file is corrupted, treat as missing
            this.content = null;
          }
        }
      }
    } catch (error) {
      // Ignore cache loading errors
    }

    this.cacheLoaded = true;
  }

  private async updateCache(): Promise<void> {
    try {
      // In a real implementation, you'd use fetch or a similar HTTP client
      // For now, this is a placeholder
      const response = await fetch(ModelInfoManager.MODEL_INFO_URL);
      if (response.ok) {
        this.content = await response.json();
        try {
          fs.writeFileSync(this.cacheFile, JSON.stringify(this.content, null, 4));
        } catch (error) {
          // Ignore cache save errors
        }
      }
    } catch (error) {
      console.error('Error fetching model info:', error);
      try {
        fs.writeFileSync(this.cacheFile, '{}');
      } catch (writeError) {
        // Ignore write errors
      }
    }
  }

  public async getModelInfo(modelName: string): Promise<ModelInfo> {
    this.loadCache();

    if (!this.content) {
      await this.updateCache();
    }

    const info = this.content?.[modelName] || this.localModelMetadata[modelName] || {};

    return {
      maxInputTokens: info.maxInputTokens || 4096,
      maxOutputTokens: info.maxOutputTokens || 1024,
      inputCostPer1kTokens: info.inputCostPer1kTokens || 0,
      outputCostPer1kTokens: info.outputCostPer1kTokens || 0,
      litellmProvider: info.litellmProvider,
      mode: info.mode,
      supportsVision: info.supportsVision || false,
      supportsToolCalls: info.supportsToolCalls || true,
      ...info,
    };
  }

  public addLocalModelMetadata(modelName: string, metadata: ModelInfo): void {
    this.localModelMetadata[modelName] = metadata;
  }
}

export class Model implements ModelSettings {
  public name: string;
  public editFormat: string = 'whole';
  public weakModelName?: string;
  public useRepoMap: boolean = false;
  public sendUndoReply: boolean = false;
  public lazy: boolean = false;
  public overeager: boolean = false;
  public reminder: string = 'user';
  public examplesAsSysMsg: boolean = false;
  public extraParams?: Record<string, any>;
  public cacheControl: boolean = false;
  public cachesByDefault: boolean = false;
  public useSystemPrompt: boolean = true;
  public useTemperature: boolean | number = true;
  public streaming: boolean = true;
  public editorModelName?: string;
  public editorEditFormat?: string;
  public reasoningTag?: string;
  public removeReasoning?: string;
  public systemPromptPrefix?: string;
  public acceptsSettings?: string[] = [];

  public verbose: boolean;
  public maxChatHistoryTokens: number = 1024;
  public weakModel?: Model;
  public editorModel?: Model;
  public info: ModelInfo = {};
  public missingKeys?: string[];
  public keysInEnvironment?: string[];

  private static modelInfoManager = new ModelInfoManager();

  constructor(
    model: string,
    weakModel?: string | boolean,
    editorModel?: string | boolean,
    editorEditFormat?: string,
    verbose: boolean = false
  ) {
    // Map any alias to its canonical name
    this.name = MODEL_ALIASES[model] || model;
    this.verbose = verbose;

    this.loadModelInfo();
    this.validateEnvironment();
    this.configureModelSettings();

    if (weakModel === false) {
      this.weakModelName = undefined;
    } else if (typeof weakModel === 'string') {
      this.getWeakModel(weakModel);
    }

    if (editorModel === false) {
      this.editorModelName = undefined;
    } else if (typeof editorModel === 'string') {
      this.getEditorModel(editorModel, editorEditFormat);
    }
  }

  private async loadModelInfo(): Promise<void> {
    this.info = await Model.modelInfoManager.getModelInfo(this.name);

    const maxInputTokens = this.info.maxInputTokens || 0;
    // Calculate max_chat_history_tokens as 1/16th of max_input_tokens,
    // with minimum 1k and maximum 8k
    this.maxChatHistoryTokens = Math.min(Math.max(maxInputTokens / 16, 1024), 8192);
  }

  private validateEnvironment(): ValidationResult {
    const result: ValidationResult = {
      missingKeys: [],
      keysInEnvironment: [],
    };

    // Check for required environment variables based on model provider
    const modelLower = this.name.toLowerCase();

    if (modelLower.includes('gpt') || modelLower.includes('openai')) {
      const key = process.env.OPENAI_API_KEY;
      if (key) {
        result.keysInEnvironment!.push('OPENAI_API_KEY');
      } else {
        result.missingKeys!.push('OPENAI_API_KEY');
      }
    }

    if (modelLower.includes('claude') || modelLower.includes('anthropic')) {
      const key = process.env.ANTHROPIC_API_KEY;
      if (key) {
        result.keysInEnvironment!.push('ANTHROPIC_API_KEY');
      } else {
        result.missingKeys!.push('ANTHROPIC_API_KEY');
      }
    }

    if (modelLower.includes('gemini')) {
      const key = process.env.GOOGLE_API_KEY;
      if (key) {
        result.keysInEnvironment!.push('GOOGLE_API_KEY');
      } else {
        result.missingKeys!.push('GOOGLE_API_KEY');
      }
    }

    this.missingKeys = result.missingKeys;
    this.keysInEnvironment = result.keysInEnvironment;

    return result;
  }

  private configureModelSettings(): void {
    const modelLower = this.name.toLowerCase();

    // Apply generic settings based on model type
    if (modelLower.includes('gpt-4') || modelLower.includes('o1')) {
      this.useRepoMap = true;
      this.lazy = false;
    } else if (modelLower.includes('claude')) {
      this.useRepoMap = true;
      this.cacheControl = true;
    } else if (modelLower.includes('gemini')) {
      this.useRepoMap = true;
    }

    // Configure edit format based on model capabilities
    if (modelLower.includes('o1')) {
      this.editFormat = 'whole';
      this.useTemperature = false;
    }
  }

  private getWeakModel(weakModelName: string): void {
    try {
      this.weakModel = new Model(weakModelName, false, false, undefined, this.verbose);
      this.weakModelName = this.weakModel.name;
    } catch (error) {
      if (this.verbose) {
        console.warn(`Failed to create weak model ${weakModelName}:`, error);
      }
    }
  }

  private getEditorModel(editorModelName: string, editorEditFormat?: string): void {
    try {
      this.editorModel = new Model(
        editorModelName,
        false,
        false,
        editorEditFormat,
        this.verbose
      );
      this.editorModelName = this.editorModel.name;
      if (editorEditFormat) {
        this.editorEditFormat = editorEditFormat;
      }
    } catch (error) {
      if (this.verbose) {
        console.warn(`Failed to create editor model ${editorModelName}:`, error);
      }
    }
  }

  public getModelInfo(): ModelInfo {
    return this.info;
  }

  public hasApiKey(): boolean {
    return (this.keysInEnvironment?.length || 0) > 0;
  }

  public getMissingKeys(): string[] {
    return this.missingKeys || [];
  }

  public supportsVision(): boolean {
    return this.info.supportsVision || false;
  }

  public supportsToolCalls(): boolean {
    return this.info.supportsToolCalls !== false;
  }

  public getProvider(): string {
    if (this.name.includes('/')) {
      return this.name.split('/')[0];
    }

    const modelLower = this.name.toLowerCase();
    if (modelLower.includes('gpt') || modelLower.includes('o1')) {
      return 'openai';
    } else if (modelLower.includes('claude')) {
      return 'anthropic';
    } else if (modelLower.includes('gemini')) {
      return 'google';
    } else {
      return 'unknown';
    }
  }

  public calculateCost(inputTokens: number, outputTokens: number): number {
    const inputCost = (this.info.inputCostPer1kTokens || 0) * inputTokens / 1000;
    const outputCost = (this.info.outputCostPer1kTokens || 0) * outputTokens / 1000;
    return inputCost + outputCost;
  }

  public toString(): string {
    return this.name;
  }
}

// Utility functions
export function getModelByName(modelName: string): Model {
  return new Model(modelName);
}

export function listAvailableModels(): string[] {
  return [
    ...OPENAI_MODELS,
    ...ANTHROPIC_MODELS,
    ...Object.keys(MODEL_ALIASES),
  ];
}

export function isValidModel(modelName: string): boolean {
  const canonicalName = MODEL_ALIASES[modelName] || modelName;
  return listAvailableModels().includes(canonicalName) ||
         listAvailableModels().includes(modelName);
}

export function resolveModelAlias(modelName: string): string {
  return MODEL_ALIASES[modelName] || modelName;
}

export const modelInfoManager = new ModelInfoManager();

export default Model;
