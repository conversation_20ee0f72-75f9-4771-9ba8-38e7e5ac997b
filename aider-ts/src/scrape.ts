import * as fs from 'fs';
import * as path from 'path';
import { URL } from 'url';
import fetch from 'node-fetch';
import * as cheerio from 'cheerio';
import TurndownService from 'turndown';

export interface ScraperOptions {
  printError?: (message: string) => void;
  playwrightAvailable?: boolean;
  puppeteerAvailable?: boolean;
  timeout?: number;
  userAgent?: string;
  maxContentLength?: number;
}

export interface ScrapedContent {
  url: string;
  title?: string;
  content: string;
  contentType?: string;
  timestamp: Date;
}

export class Scraper {
  private printError: (message: string) => void;
  private playwrightAvailable: boolean;
  private puppeteerAvailable: boolean;
  private timeout: number;
  private userAgent: string;
  private maxContentLength: number;
  private turndownService: TurndownService;

  constructor(options: ScraperOptions = {}) {
    this.printError = options.printError || ((msg) => console.error(msg));
    this.playwrightAvailable = options.playwrightAvailable ?? false;
    this.puppeteerAvailable = options.puppeteerAvailable ?? false;
    this.timeout = options.timeout || 30000;
    this.userAgent = options.userAgent || 'Aider-TS Web Scraper 1.0';
    this.maxContentLength = options.maxContentLength || 1000000; // 1MB

    // Initialize Turndown for HTML to Markdown conversion
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
    });

    // Configure Turndown rules
    this.setupTurndownRules();
  }

  private setupTurndownRules(): void {
    // Remove script and style elements
    this.turndownService.remove(['script', 'style', 'nav', 'footer', 'aside']);

    // Custom rule for code blocks
    this.turndownService.addRule('codeBlock', {
      filter: ['pre'],
      replacement: (content, node) => {
        const codeElement = node.querySelector('code');
        if (codeElement) {
          const language = this.extractLanguageFromClass(codeElement.className);
          return `\n\n\`\`\`${language}\n${codeElement.textContent}\n\`\`\`\n\n`;
        }
        return `\n\n\`\`\`\n${content}\n\`\`\`\n\n`;
      },
    });

    // Custom rule for inline code
    this.turndownService.addRule('inlineCode', {
      filter: ['code'],
      replacement: (content) => {
        return `\`${content}\``;
      },
    });

    // Custom rule for tables
    this.turndownService.addRule('table', {
      filter: 'table',
      replacement: (content, node) => {
        return this.convertTableToMarkdown(node as Element);
      },
    });
  }

  private extractLanguageFromClass(className: string): string {
    const match = className.match(/language-(\w+)/);
    return match ? match[1] : '';
  }

  private convertTableToMarkdown(table: Element): string {
    const rows = table.querySelectorAll('tr');
    if (rows.length === 0) return '';

    let markdown = '\n\n';
    let isFirstRow = true;

    rows.forEach((row) => {
      const cells = row.querySelectorAll('td, th');
      const rowData = Array.from(cells).map((cell) => cell.textContent?.trim() || '');

      markdown += '| ' + rowData.join(' | ') + ' |\n';

      if (isFirstRow) {
        markdown += '| ' + rowData.map(() => '---').join(' | ') + ' |\n';
        isFirstRow = false;
      }
    });

    return markdown + '\n';
  }

  public async scrape(url: string): Promise<string | null> {
    try {
      // Validate URL
      const parsedUrl = new URL(url);
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        this.printError(`Unsupported protocol: ${parsedUrl.protocol}`);
        return null;
      }

      // Try different scraping methods in order of preference
      let content = await this.tryBrowserScraping(url);
      if (content) return content;

      content = await this.tryHttpScraping(url);
      if (content) return content;

      this.printError(`Failed to scrape content from ${url}`);
      return null;
    } catch (error) {
      this.printError(`Error scraping ${url}: ${error}`);
      return null;
    }
  }

  private async tryBrowserScraping(url: string): Promise<string | null> {
    // Try Playwright first if available
    if (this.playwrightAvailable) {
      try {
        const content = await this.scrapeWithPlaywright(url);
        if (content) return content;
      } catch (error) {
        this.printError(`Playwright scraping failed: ${error}`);
      }
    }

    // Try Puppeteer as fallback
    if (this.puppeteerAvailable) {
      try {
        const content = await this.scrapeWithPuppeteer(url);
        if (content) return content;
      } catch (error) {
        this.printError(`Puppeteer scraping failed: ${error}`);
      }
    }

    return null;
  }

  private async scrapeWithPlaywright(url: string): Promise<string | null> {
    try {
      // Dynamic import to avoid issues if playwright is not installed
      const { chromium } = await import('playwright');

      const browser = await chromium.launch();
      const page = await browser.newPage();

      await page.setUserAgent(this.userAgent);
      await page.goto(url, { waitUntil: 'networkidle', timeout: this.timeout });

      // Wait for content to load
      await page.waitForTimeout(2000);

      const content = await page.content();
      await browser.close();

      return this.extractTextContent(content, url);
    } catch (error) {
      throw new Error(`Playwright error: ${error}`);
    }
  }

  private async scrapeWithPuppeteer(url: string): Promise<string | null> {
    try {
      // Dynamic import to avoid issues if puppeteer is not installed
      const puppeteer = await import('puppeteer-core');

      const browser = await puppeteer.launch();
      const page = await browser.newPage();

      await page.setUserAgent(this.userAgent);
      await page.goto(url, { waitUntil: 'networkidle2', timeout: this.timeout });

      const content = await page.content();
      await browser.close();

      return this.extractTextContent(content, url);
    } catch (error) {
      throw new Error(`Puppeteer error: ${error}`);
    }
  }

  private async tryHttpScraping(url: string): Promise<string | null> {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate',
          'Cache-Control': 'no-cache',
        },
        timeout: this.timeout,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type') || '';

      // Check if it's HTML content
      if (!contentType.includes('text/html')) {
        this.printError(`Unsupported content type: ${contentType}`);
        return null;
      }

      const html = await response.text();

      if (html.length > this.maxContentLength) {
        this.printError(`Content too large: ${html.length} bytes`);
        return null;
      }

      return this.extractTextContent(html, url);
    } catch (error) {
      throw new Error(`HTTP scraping error: ${error}`);
    }
  }

  private extractTextContent(html: string, url: string): string {
    const $ = cheerio.load(html);

    // Remove unwanted elements
    $('script, style, nav, footer, aside, .advertisement, .ads, .sidebar').remove();

    // Try to find main content area
    let contentElement = $('main, article, .content, .post, .entry, #content, #main');

    if (contentElement.length === 0) {
      // Fallback to body if no main content area found
      contentElement = $('body');
    }

    // Extract title
    const title = $('title').text().trim() || $('h1').first().text().trim();

    // Convert HTML to Markdown
    let markdown = this.turndownService.turndown(contentElement.html() || '');

    // Clean up the markdown
    markdown = this.cleanMarkdown(markdown);

    // Add title and URL if available
    let result = '';
    if (title) {
      result += `# ${title}\n\n`;
    }
    result += `Source: ${url}\n\n`;
    result += markdown;

    return result.trim();
  }

  private cleanMarkdown(markdown: string): string {
    // Remove excessive whitespace
    markdown = markdown.replace(/\n{3,}/g, '\n\n');

    // Remove empty links
    markdown = markdown.replace(/\[]\(\)/g, '');

    // Clean up list items
    markdown = markdown.replace(/^\s*[-*+]\s*$/gm, '');

    // Remove trailing spaces
    markdown = markdown.replace(/ +$/gm, '');

    return markdown.trim();
  }

  public async scrapeMultiple(urls: string[]): Promise<ScrapedContent[]> {
    const results: ScrapedContent[] = [];

    for (const url of urls) {
      try {
        const content = await this.scrape(url);
        if (content) {
          results.push({
            url,
            content,
            timestamp: new Date(),
          });
        }
      } catch (error) {
        this.printError(`Failed to scrape ${url}: ${error}`);
      }
    }

    return results;
  }

  public isValidUrl(url: string): boolean {
    try {
      const parsed = new URL(url);
      return ['http:', 'https:'].includes(parsed.protocol);
    } catch {
      return false;
    }
  }

  public extractUrls(text: string): string[] {
    const urlRegex = /https?:\/\/[^\s<>"']+/gi;
    const matches = text.match(urlRegex);
    return matches ? [...new Set(matches)] : [];
  }
}

// Utility functions
export function hasPlaywright(): boolean {
  try {
    require.resolve('playwright');
    return true;
  } catch {
    return false;
  }
}

export function hasPuppeteer(): boolean {
  try {
    require.resolve('puppeteer-core');
    return true;
  } catch {
    return false;
  }
}

export async function quickScrape(url: string): Promise<string | null> {
  const scraper = new Scraper({
    playwrightAvailable: hasPlaywright(),
    puppeteerAvailable: hasPuppeteer(),
  });

  return await scraper.scrape(url);
}

export default Scraper;
