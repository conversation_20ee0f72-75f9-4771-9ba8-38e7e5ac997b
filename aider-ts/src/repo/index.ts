import * as fs from 'fs';
import * as path from 'path';
import { SimpleGit, simpleGit, StatusResult, DiffResult } from 'simple-git';
import { EventEmitter } from 'events';

export interface GitRepoOptions {
  io: any; // InputOutput instance
  fnames?: string[];
  gitDname?: string;
  aiderIgnoreFile?: string;
  models?: any;
  attributeAuthor?: boolean;
  attributeCommitter?: boolean;
  attributeCommitMessageAuthor?: boolean;
  attributeCommitMessageCommitter?: boolean;
  commitPrompt?: string;
  subtreeOnly?: boolean;
  gitCommitVerify?: boolean;
  attributeCoAuthoredBy?: boolean;
}

export interface CommitOptions {
  fnames?: string[];
  context?: string;
  message?: string;
  aiderEdits?: boolean;
  coder?: any;
}

export class GitRepo extends EventEmitter {
  private git: SimpleGit | null = null;
  private io: any;
  private models: any;
  private root: string;
  private normalizedPath: Map<string, string> = new Map();
  private treeFiles: Set<string> = new Set();
  private ignoreFileCache: Map<string, boolean> = new Map();

  public aiderIgnoreFile?: string;
  public aiderIgnoreSpec: any = null;
  public aiderIgnoreTs: number = 0;
  public aiderIgnoreLastCheck: number = 0;
  public subtreeOnly: boolean = false;
  public gitRepoError: string | null = null;

  public attributeAuthor: boolean;
  public attributeCommitter: boolean;
  public attributeCommitMessageAuthor: boolean;
  public attributeCommitMessageCommitter: boolean;
  public attributeCoAuthoredBy: boolean;
  public commitPrompt?: string;
  public gitCommitVerify: boolean;

  constructor(options: GitRepoOptions) {
    super();

    this.io = options.io;
    this.models = options.models;
    this.attributeAuthor = options.attributeAuthor ?? true;
    this.attributeCommitter = options.attributeCommitter ?? true;
    this.attributeCommitMessageAuthor = options.attributeCommitMessageAuthor ?? false;
    this.attributeCommitMessageCommitter = options.attributeCommitMessageCommitter ?? false;
    this.attributeCoAuthoredBy = options.attributeCoAuthoredBy ?? false;
    this.commitPrompt = options.commitPrompt;
    this.subtreeOnly = options.subtreeOnly ?? false;
    this.gitCommitVerify = options.gitCommitVerify ?? true;

    const checkFnames = options.gitDname ? [options.gitDname] :
                       options.fnames ? options.fnames : ['.'];

    this.initializeRepo(checkFnames);

    if (options.aiderIgnoreFile) {
      this.aiderIgnoreFile = path.resolve(options.aiderIgnoreFile);
    }
  }

  private initializeRepo(checkFnames: string[]): void {
    try {
      // Find the git repository root
      let repoRoot: string | null = null;

      for (const fname of checkFnames) {
        try {
          const dirPath = fs.statSync(fname).isDirectory() ? fname : path.dirname(fname);
          const absolutePath = path.resolve(dirPath);

          // Walk up the directory tree to find .git
          let currentPath = absolutePath;
          while (currentPath !== path.dirname(currentPath)) {
            if (fs.existsSync(path.join(currentPath, '.git'))) {
              repoRoot = currentPath;
              break;
            }
            currentPath = path.dirname(currentPath);
          }

          if (repoRoot) break;
        } catch (error) {
          // Continue to next file
        }
      }

      if (repoRoot) {
        this.root = repoRoot;
        this.git = simpleGit(repoRoot);
        this.loadTrackedFiles();
      } else {
        this.gitRepoError = 'No git repository found';
        this.io?.toolWarning?.('No git repository found');
      }
    } catch (error) {
      this.gitRepoError = `Git repository initialization failed: ${error}`;
      this.io?.toolError?.(this.gitRepoError);
    }
  }

  private async loadTrackedFiles(): Promise<void> {
    if (!this.git) return;

    try {
      const files = await this.git.raw(['ls-tree', '-r', '--name-only', 'HEAD']);
      this.treeFiles = new Set(files.split('\n').filter(f => f.trim()));
    } catch (error) {
      // Repository might be empty or HEAD doesn't exist yet
      this.treeFiles = new Set();
    }
  }

  public async commit(options: CommitOptions = {}): Promise<string | null> {
    if (!this.git) {
      this.io?.toolError?.('No git repository available');
      return null;
    }

    try {
      const { fnames, context, message, aiderEdits = false } = options;

      // Get files to commit
      let filesToCommit = fnames;
      if (!filesToCommit) {
        const dirty = await this.getDirtyFiles();
        filesToCommit = dirty;
      }

      if (!filesToCommit || filesToCommit.length === 0) {
        this.io?.toolOutput?.('No changes to commit');
        return null;
      }

      // Add files to staging
      for (const file of filesToCommit) {
        await this.git.add(file);
      }

      // Generate commit message if not provided
      let commitMessage = message;
      if (!commitMessage) {
        const diffs = await this.getDiffs(filesToCommit);
        commitMessage = await this.getCommitMessage(diffs, context);
      }

      // Set author and committer if configured
      const commitOptions: any = {};

      if (this.attributeAuthor) {
        commitOptions['--author'] = 'aider <<EMAIL>>';
      }

      // Perform the commit
      const result = await this.git.commit(commitMessage, undefined, commitOptions);

      if (result.commit) {
        this.io?.toolOutput?.(`Committed ${result.commit}: ${commitMessage}`);
        return result.commit;
      }

      return null;
    } catch (error) {
      this.io?.toolError?.(`Git commit failed: ${error}`);
      return null;
    }
  }

  public async getDiffs(fnames?: string[]): Promise<string> {
    if (!this.git) return '';

    try {
      let diffArgs = ['--no-index', '--no-prefix'];

      if (fnames && fnames.length > 0) {
        // Get diffs for specific files
        const diffs: string[] = [];
        for (const fname of fnames) {
          try {
            const diff = await this.git.diff(['HEAD', '--', fname]);
            if (diff) {
              diffs.push(`--- ${fname}\n+++ ${fname}\n${diff}`);
            }
          } catch (error) {
            // File might be new
            if (fs.existsSync(fname)) {
              const content = fs.readFileSync(fname, 'utf-8');
              diffs.push(`--- /dev/null\n+++ ${fname}\n${content.split('\n').map(line => `+${line}`).join('\n')}`);
            }
          }
        }
        return diffs.join('\n\n');
      } else {
        // Get all diffs
        return await this.git.diff(['--cached']) || await this.git.diff();
      }
    } catch (error) {
      this.io?.toolError?.(`Unable to get diffs: ${error}`);
      return '';
    }
  }

  public async diffCommits(pretty: boolean, fromCommit: string, toCommit: string): Promise<string> {
    if (!this.git) return '';

    try {
      const args = [];
      if (pretty) {
        args.push('--color=always');
      }
      args.push(fromCommit, toCommit);

      return await this.git.diff(args);
    } catch (error) {
      this.io?.toolError?.(`Unable to diff commits: ${error}`);
      return '';
    }
  }

  public async getTrackedFiles(): Promise<string[]> {
    if (!this.git) return [];

    try {
      const files = await this.git.raw(['ls-files']);
      return files.split('\n').filter(f => f.trim()).map(f => this.normalizePath(f));
    } catch (error) {
      return [];
    }
  }

  public normalizePath(filePath: string): string {
    const original = filePath;
    const cached = this.normalizedPath.get(original);
    if (cached) return cached;

    let normalized: string;

    if (path.isAbsolute(filePath)) {
      normalized = path.relative(this.root || process.cwd(), filePath);
    } else {
      normalized = filePath;
    }

    // Normalize path separators
    normalized = normalized.replace(/\\/g, '/');

    this.normalizedPath.set(original, normalized);
    return normalized;
  }

  public refreshAiderIgnore(): void {
    if (!this.aiderIgnoreFile) return;

    try {
      const now = Date.now();
      if (now - this.aiderIgnoreLastCheck < 1000) return; // Check at most once per second

      this.aiderIgnoreLastCheck = now;

      if (fs.existsSync(this.aiderIgnoreFile)) {
        const stats = fs.statSync(this.aiderIgnoreFile);
        const mtime = stats.mtime.getTime();

        if (mtime !== this.aiderIgnoreTs) {
          this.aiderIgnoreTs = mtime;
          const content = fs.readFileSync(this.aiderIgnoreFile, 'utf-8');
          // In a real implementation, you'd use a proper gitignore parser
          this.aiderIgnoreSpec = {
            patterns: content.split('\n').filter(line => line.trim() && !line.startsWith('#')),
            match: (filePath: string) => {
              return this.aiderIgnoreSpec.patterns.some((pattern: string) => {
                return filePath.includes(pattern) || filePath.match(new RegExp(pattern.replace(/\*/g, '.*')));
              });
            }
          };
        }
      }
    } catch (error) {
      this.io?.toolWarning?.(`Error refreshing aider ignore: ${error}`);
    }
  }

  public ignoredFile(fname: string): boolean {
    this.refreshAiderIgnore();

    // Check git ignore
    if (this.gitIgnoredFile(fname)) return true;

    // Check aider ignore
    if (this.aiderIgnoreSpec?.match(fname)) return true;

    return false;
  }

  public gitIgnoredFile(filePath: string): boolean {
    if (!this.git) return false;

    const cached = this.ignoreFileCache.get(filePath);
    if (cached !== undefined) return cached;

    try {
      // Use git check-ignore to determine if file is ignored
      const result = this.git.raw(['check-ignore', filePath]);
      const ignored = result.length > 0;
      this.ignoreFileCache.set(filePath, ignored);
      return ignored;
    } catch (error) {
      this.ignoreFileCache.set(filePath, false);
      return false;
    }
  }

  public pathInRepo(filePath: string): boolean {
    if (!this.git) return false;
    const normalized = this.normalizePath(filePath);
    return this.treeFiles.has(normalized);
  }

  public absoluteRootPath(filePath: string): string {
    return path.resolve(this.root || process.cwd(), filePath);
  }

  public async getDirtyFiles(): Promise<string[]> {
    if (!this.git) return [];

    try {
      const status = await this.git.status();
      const dirtyFiles = [
        ...status.modified,
        ...status.created,
        ...status.deleted,
        ...status.renamed.map(r => r.to),
        ...status.staged
      ];

      return [...new Set(dirtyFiles)].map(f => this.normalizePath(f));
    } catch (error) {
      return [];
    }
  }

  public async isDirty(filePath?: string): Promise<boolean> {
    if (!this.git) return true;

    if (filePath && !this.pathInRepo(filePath)) {
      return true;
    }

    try {
      const status = await this.git.status([filePath].filter(Boolean));
      return !status.isClean();
    } catch (error) {
      return true;
    }
  }

  public async getHeadCommit(): Promise<any> {
    if (!this.git) return null;

    try {
      const log = await this.git.log(['-1']);
      return log.latest;
    } catch (error) {
      return null;
    }
  }

  public async getHeadCommitSha(short: boolean = false): Promise<string | null> {
    const commit = await this.getHeadCommit();
    if (!commit) return null;

    return short ? commit.hash.substring(0, 7) : commit.hash;
  }

  public async getHeadCommitMessage(defaultMessage?: string): Promise<string | null> {
    const commit = await this.getHeadCommit();
    if (!commit) return defaultMessage || null;

    return commit.message;
  }

  private async getCommitMessage(diffs: string, context?: string, userLanguage?: string): Promise<string> {
    // This would integrate with the LLM to generate commit messages
    // For now, return a simple default message
    const diffLines = diffs.split('\n').length;

    if (context) {
      return `${context}\n\nChanges: ${diffLines} lines modified`;
    }

    return `Update files: ${diffLines} lines modified`;
  }

  public getRelRepoDir(): string {
    if (!this.git || !this.root) return '.git';

    try {
      return path.relative(process.cwd(), path.join(this.root, '.git'));
    } catch (error) {
      return '.git';
    }
  }

  public async getRemoteOriginUrl(): Promise<string | null> {
    if (!this.git) return null;

    try {
      const remotes = await this.git.getRemotes(true);
      const origin = remotes.find(r => r.name === 'origin');
      return origin?.refs?.fetch || null;
    } catch (error) {
      return null;
    }
  }

  public isRepo(): boolean {
    return this.git !== null && !this.gitRepoError;
  }

  public getRoot(): string {
    return this.root || process.cwd();
  }

  public async getCurrentBranch(): Promise<string | null> {
    if (!this.git) return null;

    try {
      const status = await this.git.status();
      return status.current || null;
    } catch (error) {
      return null;
    }
  }

  public async createBranch(branchName: string): Promise<boolean> {
    if (!this.git) return false;

    try {
      await this.git.checkoutLocalBranch(branchName);
      return true;
    } catch (error) {
      this.io?.toolError?.(`Failed to create branch ${branchName}: ${error}`);
      return false;
    }
  }

  public async switchBranch(branchName: string): Promise<boolean> {
    if (!this.git) return false;

    try {
      await this.git.checkout(branchName);
      return true;
    } catch (error) {
      this.io?.toolError?.(`Failed to switch to branch ${branchName}: ${error}`);
      return false;
    }
  }
}

export default GitRepo;
