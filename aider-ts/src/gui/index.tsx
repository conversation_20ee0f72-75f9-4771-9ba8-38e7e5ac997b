import React, { useState, useEffect, useCallback, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GuiP<PERSON>, Message } from "./controller";
import { Coder } from "../coders/index";
import * as urls from "../urls";

interface GuiComponentProps {
  coder: Coder;
}

interface SidebarProps {
  controller: G<PERSON><PERSON>ontroller;
  disabled: boolean;
}

interface MessageItemProps {
  message: Message;
  onUndo?: (commitHash: string) => void;
  canUndo?: (commitHash: string) => boolean;
}

interface FileInputProps {
  availableFiles: string[];
  selectedFiles: string[];
  onFilesChange: (files: string[]) => void;
  disabled: boolean;
}

interface WebInputProps {
  onSubmit: (url: string) => void;
  disabled: boolean;
}

interface ChatInputProps {
  onSubmit: (message: string) => void;
  disabled: boolean;
}

// File selector component
const FileInput: React.FC<FileInputProps> = ({
  availableFiles,
  selectedFiles,
  onFilesChange,
  disabled,
}) => {
  const handleFileToggle = (file: string) => {
    if (selectedFiles.includes(file)) {
      onFilesChange(selectedFiles.filter((f) => f !== file));
    } else {
      onFilesChange([...selectedFiles, file]);
    }
  };

  return (
    <div className="file-input">
      <label className="form-label">Add files to the chat</label>
      <div className="file-list">
        {availableFiles.map((file) => (
          <div key={file} className="file-item">
            <input
              type="checkbox"
              id={`file-${file}`}
              checked={selectedFiles.includes(file)}
              onChange={() => handleFileToggle(file)}
              disabled={disabled}
            />
            <label htmlFor={`file-${file}`} className="file-label">
              {file}
            </label>
          </div>
        ))}
      </div>
      <small className="help-text">
        Only add the files that need to be *edited* for the task you are working
        on. Aider will pull in other relevant code to provide context to the
        LLM.
      </small>
    </div>
  );
};

// Web page input component
const WebInput: React.FC<WebInputProps> = ({ onSubmit, disabled }) => {
  const [url, setUrl] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url.trim()) {
      onSubmit(url.trim());
      setUrl("");
      setIsOpen(false);
    }
  };

  return (
    <div className="web-input">
      <button
        className="btn btn-secondary"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
      >
        Add a web page to the chat
      </button>

      {isOpen && (
        <div className="web-input-modal">
          <h4>Add the text content of a web page to the chat</h4>
          <form onSubmit={handleSubmit}>
            <input
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://..."
              className="form-input"
              disabled={disabled}
            />
            <div className="form-actions">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={!url.trim()}
              >
                Add Page
              </button>
              <button
                type="button"
                className="btn btn-secondary"
                onClick={() => setIsOpen(false)}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

// Message item component
const MessageItem: React.FC<MessageItemProps> = ({
  message,
  onUndo,
  canUndo,
}) => {
  const [showDiff, setShowDiff] = useState(false);

  const renderEditInfo = () => {
    if (!message.edit) return null;

    const { commit_hash, commit_message, diff, fnames } = message.edit;

    if (!commit_hash && !fnames) return null;

    const showUndoButton = commit_hash && canUndo && canUndo(commit_hash);
    let content = "";

    if (commit_hash) {
      content += `Commit \`${commit_hash}\`: ${commit_message}  \n`;
    }

    if (fnames && fnames.length > 0) {
      const fileList = fnames.map((f) => `\`${f}\``).join(", ");
      content += `Applied edits to ${fileList}.`;
    }

    return (
      <div className="edit-info">
        <div className="edit-content">{content}</div>

        {diff && (
          <div className="diff-section">
            <button
              className="btn btn-link"
              onClick={() => setShowDiff(!showDiff)}
            >
              {showDiff ? "Hide" : "Show"} Diff
            </button>
            {showDiff && (
              <pre className="diff-content">
                <code>{diff}</code>
              </pre>
            )}
          </div>
        )}

        {showUndoButton && onUndo && (
          <button
            className="btn btn-warning btn-sm"
            onClick={() => onUndo(commit_hash)}
          >
            Undo commit `{commit_hash}`
          </button>
        )}
      </div>
    );
  };

  switch (message.role) {
    case "edit":
      return <div className="message edit-message">{renderEditInfo()}</div>;

    case "info":
      return (
        <div className="message info-message">
          <div className="alert alert-info">{message.content}</div>
        </div>
      );

    case "text":
      const firstLine = message.content.split("\n")[0];
      return (
        <div className="message text-message">
          <details>
            <summary>{firstLine}</summary>
            <pre>{message.content}</pre>
          </details>
        </div>
      );

    case "user":
      return (
        <div className="message user-message">
          <div className="message-header">User</div>
          <div className="message-content">{message.content}</div>
        </div>
      );

    case "assistant":
      return (
        <div className="message assistant-message">
          <div className="message-header">Assistant</div>
          <div className="message-content">{message.content}</div>
        </div>
      );

    default:
      return (
        <div className="message unknown-message">
          <pre>{JSON.stringify(message, null, 2)}</pre>
        </div>
      );
  }
};

// Chat input component
const ChatInput: React.FC<ChatInputProps> = ({ onSubmit, disabled }) => {
  const [message, setMessage] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      onSubmit(message.trim());
      setMessage("");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="chat-input-form">
      <div className="input-group">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Say something"
          className="form-input chat-input"
          disabled={disabled}
        />
        <button
          type="submit"
          className="btn btn-primary"
          disabled={disabled || !message.trim()}
        >
          Send
        </button>
      </div>
    </form>
  );
};

// Sidebar component
const Sidebar: React.FC<SidebarProps> = ({ controller, disabled }) => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>(
    controller.inchatFiles,
  );
  const [recentMessage, setRecentMessage] = useState<string>("");

  const handleFilesChange = (files: string[]) => {
    setSelectedFiles(files);
    controller.addFiles(files);
  };

  const handleWebPageSubmit = async (url: string) => {
    await controller.addWebPage(url);
  };

  const handleClearHistory = () => {
    controller.clearChatHistory();
  };

  const handleRecentMessageSelect = async (message: string) => {
    if (message) {
      await controller.submitPrompt(message);
      setRecentMessage("");
    }
  };

  return (
    <div className="sidebar">
      <h1>Aider</h1>

      <div className="sidebar-section">
        <FileInput
          availableFiles={controller.availableFiles}
          selectedFiles={selectedFiles}
          onFilesChange={handleFilesChange}
          disabled={disabled}
        />
      </div>

      <div className="sidebar-section">
        <WebInput onSubmit={handleWebPageSubmit} disabled={disabled} />
      </div>

      <div className="sidebar-section">
        <label className="form-label">Resend a recent chat message</label>
        <select
          value={recentMessage}
          onChange={(e) => setRecentMessage(e.target.value)}
          className="form-select"
          disabled={disabled}
        >
          <option value="">Choose a recent chat message</option>
          {controller.inputHistory.map((msg, index) => (
            <option key={index} value={msg}>
              {msg.length > 50 ? msg.substring(0, 47) + "..." : msg}
            </option>
          ))}
        </select>
        {recentMessage && (
          <button
            className="btn btn-secondary btn-sm mt-2"
            onClick={() => handleRecentMessageSelect(recentMessage)}
            disabled={disabled}
          >
            Resend Message
          </button>
        )}
      </div>

      <div className="sidebar-section">
        <button
          className="btn btn-danger"
          onClick={handleClearHistory}
          disabled={disabled}
        >
          Clear chat history
        </button>
        <small className="help-text">Saves tokens, reduces confusion</small>
      </div>

      <div className="sidebar-section">
        <div className="alert alert-warning">
          <small>
            This browser version of aider is experimental. Please share feedback
            in{" "}
            <a
              href="https://github.com/Aider-AI/aider/issues"
              target="_blank"
              rel="noopener noreferrer"
            >
              GitHub issues
            </a>
            .
          </small>
        </div>
      </div>
    </div>
  );
};

// Main GUI component
const AiderGui: React.FC<GuiComponentProps> = ({ coder }) => {
  const [controller] = useState(() => new GuiController(coder));
  const [messages, setMessages] = useState<Message[]>(controller.messages);
  const [isPromptPending, setIsPromptPending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Update messages when controller state changes
  useEffect(() => {
    const interval = setInterval(() => {
      setMessages([...controller.messages]);
      setIsPromptPending(controller.isPromptPending);
    }, 100);

    return () => clearInterval(interval);
  }, [controller]);

  const handleChatSubmit = async (message: string) => {
    await controller.submitPrompt(message);
  };

  const handleUndo = async (commitHash: string) => {
    await controller.undoCommit(commitHash);
  };

  return (
    <div className="aider-gui">
      <div className="gui-layout">
        <Sidebar controller={controller} disabled={isPromptPending} />

        <div className="main-content">
          <div className="messages-container">
            {messages.map((message, index) => (
              <MessageItem
                key={index}
                message={message}
                onUndo={handleUndo}
                canUndo={controller.canUndo.bind(controller)}
              />
            ))}
            <div ref={messagesEndRef} />
          </div>

          <div className="chat-input-container">
            <ChatInput onSubmit={handleChatSubmit} disabled={isPromptPending} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AiderGui;
export { GuiController, FileInput, WebInput, MessageItem, ChatInput, Sidebar };
