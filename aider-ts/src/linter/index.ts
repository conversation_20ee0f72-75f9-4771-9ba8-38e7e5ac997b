import * as fs from 'fs';
import * as path from 'path';
import { spawn, SpawnOptions } from 'child_process';
import { EventEmitter } from 'events';

export interface LintResult {
  text: string;
  lines: number[];
  fileName?: string;
}

export interface LinterOptions {
  encoding?: string;
  root?: string;
}

export interface LanguageLinter {
  (fileName: string, code: string): Promise<LintResult | null>;
}

export class Linter extends EventEmitter {
  private encoding: string;
  private root?: string;
  private languages: Map<string, LanguageLinter | string> = new Map();
  private allLintCmd?: string;

  constructor(options: LinterOptions = {}) {
    super();
    this.encoding = options.encoding || 'utf-8';
    this.root = options.root;

    // Set up default language linters
    this.setupDefaultLinters();
  }

  private setupDefaultLinters(): void {
    this.languages.set('python', this.pythonLint.bind(this));
    this.languages.set('javascript', this.javascriptLint.bind(this));
    this.languages.set('typescript', this.typescriptLint.bind(this));
    this.languages.set('json', this.jsonLint.bind(this));
  }

  public setLinter(lang: string | null, cmd: string | LanguageLinter): void {
    if (lang) {
      this.languages.set(lang, cmd);
    } else {
      this.allLintCmd = typeof cmd === 'string' ? cmd : undefined;
    }
  }

  public getRelativeFileName(fileName: string): string {
    if (this.root) {
      try {
        return path.relative(this.root, fileName);
      } catch (error) {
        return fileName;
      }
    }
    return fileName;
  }

  public async runCmd(cmd: string, relFileName: string, code: string): Promise<LintResult | null> {
    const fullCmd = `${cmd} "${relFileName}"`;

    try {
      const { stdout, stderr, exitCode } = await this.executeCommand(fullCmd);

      if (exitCode === 0) {
        return null; // No errors
      }

      const errors = stderr || stdout;
      const result = `## Running: ${fullCmd}\n\n${errors}`;

      return this.errorsToLintResult(relFileName, result);
    } catch (error) {
      console.error(`Unable to execute lint command: ${error}`);
      return null;
    }
  }

  private executeCommand(cmd: string): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    return new Promise((resolve, reject) => {
      const options: SpawnOptions = {
        cwd: this.root,
        shell: true,
        encoding: 'utf8' as any,
      };

      const child = spawn(cmd, [], options);
      let stdout = '';
      let stderr = '';

      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({
          stdout,
          stderr,
          exitCode: code || 0,
        });
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  public errorsToLintResult(relFileName: string, errors: string): LintResult | null {
    if (!errors) return null;

    const lineNumbers: number[] = [];
    const filenamesAndLineNums = this.findFilenamesAndLineNumbers(errors, [relFileName]);

    if (filenamesAndLineNums.size > 0) {
      const fileLineNums = filenamesAndLineNums.get(relFileName);
      if (fileLineNums) {
        lineNumbers.push(...fileLineNums.map(num => num - 1)); // Convert to 0-based indexing
      }
    }

    return {
      text: errors,
      lines: lineNumbers,
      fileName: relFileName,
    };
  }

  public async lint(fileName: string, cmd?: string): Promise<LintResult | null> {
    const relFileName = this.getRelativeFileName(fileName);

    try {
      const code = fs.readFileSync(fileName, this.encoding);

      if (cmd?.trim()) {
        return await this.runCmd(cmd.trim(), relFileName, code);
      }

      const lang = this.getLanguageFromFileName(fileName);
      if (!lang) return null;

      const linter = this.allLintCmd || this.languages.get(lang);
      if (!linter) return null;

      if (typeof linter === 'string') {
        return await this.runCmd(linter, relFileName, code);
      } else {
        return await linter(fileName, code);
      }
    } catch (error) {
      console.error(`Unable to read ${fileName}: ${error}`);
      return null;
    }
  }

  private getLanguageFromFileName(fileName: string): string | null {
    const ext = path.extname(fileName).toLowerCase();

    const extensionMap: Record<string, string> = {
      '.py': 'python',
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.json': 'json',
      '.java': 'java',
      '.cpp': 'cpp',
      '.cc': 'cpp',
      '.cxx': 'cpp',
      '.c': 'c',
      '.h': 'c',
      '.hpp': 'cpp',
      '.cs': 'csharp',
      '.go': 'go',
      '.rs': 'rust',
      '.php': 'php',
      '.rb': 'ruby',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
    };

    return extensionMap[ext] || null;
  }

  // Language-specific linters
  private async pythonLint(fileName: string, code: string): Promise<LintResult | null> {
    // Basic Python syntax check using a simple approach
    // In a real implementation, you might want to use a Python AST parser
    try {
      const result = await this.executeCommand(`python -m py_compile "${fileName}"`);
      if (result.exitCode === 0) return null;

      return this.errorsToLintResult(fileName, result.stderr);
    } catch (error) {
      return this.basicSyntaxCheck(fileName, code);
    }
  }

  private async javascriptLint(fileName: string, code: string): Promise<LintResult | null> {
    try {
      // Try to parse as JavaScript
      new Function(code);
      return null;
    } catch (error: any) {
      const lineMatch = error.stack?.match(/at.*:(\d+):\d+/);
      const lineNumber = lineMatch ? parseInt(lineMatch[1]) - 1 : 0;

      return {
        text: `JavaScript syntax error: ${error.message}`,
        lines: [lineNumber],
        fileName,
      };
    }
  }

  private async typescriptLint(fileName: string, code: string): Promise<LintResult | null> {
    try {
      // Check if TypeScript compiler is available
      const result = await this.executeCommand(`npx tsc --noEmit "${fileName}"`);
      if (result.exitCode === 0) return null;

      return this.errorsToLintResult(fileName, result.stderr);
    } catch (error) {
      // Fallback to basic syntax check
      return this.basicSyntaxCheck(fileName, code);
    }
  }

  private async jsonLint(fileName: string, code: string): Promise<LintResult | null> {
    try {
      JSON.parse(code);
      return null;
    } catch (error: any) {
      // Extract line number from JSON parse error
      const lineMatch = error.message.match(/at position (\d+)/);
      let lineNumber = 0;

      if (lineMatch) {
        const position = parseInt(lineMatch[1]);
        const lines = code.substring(0, position).split('\n');
        lineNumber = Math.max(0, lines.length - 1);
      }

      return {
        text: `JSON syntax error: ${error.message}`,
        lines: [lineNumber],
        fileName,
      };
    }
  }

  private basicSyntaxCheck(fileName: string, code: string): LintResult | null {
    // Basic syntax checking - look for common issues
    const issues: string[] = [];
    const lines = code.split('\n');
    const problemLines: number[] = [];

    lines.forEach((line, index) => {
      // Check for unmatched brackets/braces
      const openBrackets = (line.match(/[\[\{]/g) || []).length;
      const closeBrackets = (line.match(/[\]\}]/g) || []).length;

      if (Math.abs(openBrackets - closeBrackets) > 1) {
        issues.push(`Line ${index + 1}: Potential unmatched brackets`);
        problemLines.push(index);
      }

      // Check for unterminated strings
      const singleQuotes = (line.match(/'/g) || []).length;
      const doubleQuotes = (line.match(/"/g) || []).length;

      if (singleQuotes % 2 !== 0 || doubleQuotes % 2 !== 0) {
        issues.push(`Line ${index + 1}: Potential unterminated string`);
        problemLines.push(index);
      }
    });

    if (issues.length === 0) return null;

    return {
      text: `Basic syntax check found issues:\n${issues.join('\n')}`,
      lines: problemLines,
      fileName,
    };
  }

  private findFilenamesAndLineNumbers(text: string, fileNames: string[]): Map<string, number[]> {
    const result = new Map<string, number[]>();

    // Pattern to match filename:linenumber
    for (const fileName of fileNames) {
      const escapedFileName = fileName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const pattern = new RegExp(`\\b${escapedFileName}:(\\d+)\\b`, 'g');
      const lineNumbers: number[] = [];

      let match;
      while ((match = pattern.exec(text)) !== null) {
        lineNumbers.push(parseInt(match[1]));
      }

      if (lineNumbers.length > 0) {
        result.set(fileName, lineNumbers);
      }
    }

    return result;
  }
}

// Standalone linting functions
export async function lintPythonCompile(fileName: string, code: string): Promise<LintResult | null> {
  try {
    // This would require a Python subprocess call
    // For now, return null (no errors)
    return null;
  } catch (error: any) {
    const lineNumber = error.lineNumber ? error.lineNumber - 1 : 0;
    return {
      text: `Python compile error: ${error.message}`,
      lines: [lineNumber],
      fileName,
    };
  }
}

export async function basicLint(fileName: string, code: string): Promise<LintResult | null> {
  const linter = new Linter();
  return linter.basicSyntaxCheck(fileName, code);
}

// Utility functions
export function extractLineNumbers(text: string): number[] {
  const lineNumberPattern = /line\s+(\d+)/gi;
  const matches: number[] = [];
  let match;

  while ((match = lineNumberPattern.exec(text)) !== null) {
    matches.push(parseInt(match[1]) - 1); // Convert to 0-based indexing
  }

  return matches;
}

export function formatLintResult(result: LintResult): string {
  let formatted = result.text;

  if (result.lines.length > 0) {
    formatted += `\n\nAffected lines: ${result.lines.map(l => l + 1).join(', ')}`;
  }

  return formatted;
}

// Main function for CLI usage
export async function main(fileNames: string[]): Promise<void> {
  if (fileNames.length === 0) {
    console.log('Usage: node linter.js <file1> <file2> ...');
    process.exit(1);
  }

  const linter = new Linter({ root: process.cwd() });

  for (const fileName of fileNames) {
    console.log(`\nLinting ${fileName}...`);
    const result = await linter.lint(fileName);

    if (result) {
      console.log(formatLintResult(result));
    } else {
      console.log('No issues found.');
    }
  }
}

export default Linter;
