import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { v4 as uuidv4 } from 'uuid';
import { PostHog } from 'posthog-node';

const PERCENT = 10;

function computeHexThreshold(percent: number): string {
    if (percent < 0 || percent > 100) {
        throw new Error("Percentage must be between 0 and 100");
    }
    const value = Math.round(0xFFFFFF * percent / 100);
    return value.toString(16).padStart(6, '0');
}

function isUuidInPercentage(uuidStr: string, percent: number): boolean {
    if (percent === 0) {
        return false;
    }
    if (!uuidStr) {
        return false;
    }
    const threshold = computeHexThreshold(percent);
    return uuidStr.substring(0, 6) <= threshold;
}

const posthogProjectApiKey = "phc_99T7muzafUMMZX15H8XePbMSreEUzahHbtWjy3l5Qbv";
const posthogHost = "https://us.i.posthog.com";

export class Analytics {
    private ph: PostHog | null = null;
    private userId: string | null = null;
    private permanentlyDisable = false;
    private askedOptIn = false;
    private logfile: string | null = null;
    private customPosthogHost: string | null = null;
    private customPosthogProjectApiKey: string | null = null;

    constructor(logfile: string | null = null, permanentlyDisable = false, posthogHost: string | null = null, posthogProjectApiKey: string | null = null) {
        this.logfile = logfile;
        this.getOrCreateUuid();
        this.customPosthogHost = posthogHost;
        this.customPosthogProjectApiKey = posthogProjectApiKey;

        if (this.permanentlyDisable || permanentlyDisable || !this.askedOptIn) {
            this.disable(permanentlyDisable);
        }
    }

    public enable() {
        if (!this.userId || this.permanentlyDisable || !this.askedOptIn) {
            this.disable(this.permanentlyDisable);
            return;
        }

        this.ph = new PostHog(
            this.customPosthogProjectApiKey || posthogProjectApiKey,
            {
                host: this.customPosthogHost || posthogHost,
                personalApiKey: process.env.POSTHOG_PERSONAL_API_KEY,
            }
        );
    }

    public disable(permanently: boolean) {
        this.ph = null;
        if (permanently) {
            this.askedOptIn = true;
            this.permanentlyDisable = true;
            this.saveData();
        }
    }

    public needToAsk(argsAnalytics?: boolean): boolean {
        if (argsAnalytics === false) {
            return false;
        }
        const couldAsk = !this.askedOptIn && !this.permanentlyDisable;
        if (!couldAsk) {
            return false;
        }
        if (argsAnalytics === true) {
            return true;
        }
        if (!this.userId) {
            return false;
        }
        return isUuidInPercentage(this.userId, PERCENT);
    }

    private getDataFilePath(): string | null {
        try {
            const dataDir = path.join(os.homedir(), '.aider');
            fs.mkdirSync(dataDir, { recursive: true });
            return path.join(dataDir, 'analytics.json');
        } catch (e) {
            this.disable(false);
            return null;
        }
    }

    private getOrCreateUuid() {
        this.loadData();
        if (this.userId) {
            return;
        }
        this.userId = uuidv4();
        this.saveData();
    }

    private loadData() {
        const dataFile = this.getDataFilePath();
        if (!dataFile || !fs.existsSync(dataFile)) {
            return;
        }
        try {
            const data = JSON.parse(fs.readFileSync(dataFile, 'utf-8'));
            this.permanentlyDisable = data.permanently_disable ?? false;
            this.userId = data.uuid ?? null;
            this.askedOptIn = data.asked_opt_in ?? false;
        } catch (e) {
            this.disable(false);
        }
    }

    private saveData() {
        const dataFile = this.getDataFilePath();
        if (!dataFile) {
            return;
        }
        const data = {
            uuid: this.userId,
            permanently_disable: this.permanentlyDisable,
            asked_opt_in: this.askedOptIn,
        };
        try {
            fs.writeFileSync(dataFile, JSON.stringify(data, null, 4));
        } catch (e) {
            this.disable(false);
        }
    }

    private getSystemInfo() {
        return {
            node_version: process.version,
            os_platform: os.platform(),
            os_release: os.release(),
            machine: os.arch(),
            // aider_version: __version__, // Need to get version from package.json
        };
    }

    public event(eventName: string, properties: Record<string, any> = {}) {
        if (!this.ph && !this.logfile) {
            return;
        }

        const allProperties = {
            ...this.getSystemInfo(),
            ...properties,
        };

        if (this.ph && this.userId) {
            this.ph.capture({
                distinctId: this.userId,
                event: eventName,
                properties: allProperties,
            });
        }

        if (this.logfile && this.userId) {
            const logEntry = {
                event: eventName,
                properties: allProperties,
                user_id: this.userId,
                time: Math.floor(Date.now() / 1000),
            };
            try {
                fs.appendFileSync(this.logfile, JSON.stringify(logEntry) + '\n');
            } catch (e) {
                // Ignore errors
            }
        }
    }
}
