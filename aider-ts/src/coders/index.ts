// Main exports from the coders module
export * from "./BaseCoder";
export * from "./registry";

// Individual coder implementations
export { ArchitectCoder } from "./architect_coder";
export { AskCoder } from "./ask_coder";
export { ContextCoder } from "./context_coder";
export { EditBlockCoder } from "./editblock_coder";
export { EditBlockFencedCoder } from "./editblock_fenced_coder";
export { EditorDiffFencedCoder } from "./editor_diff_fenced_coder";
export { EditorEditBlockCoder } from "./editor_editblock_coder";
export { EditorWholeFileCoder } from "./editor_whole_coder";
export { HelpCoder } from "./help_coder";
export { PatchCoder } from "./patch_coder";
export { UnifiedDiffCoder } from "./udiff_coder";
export { UnifiedDiffSimpleCoder } from "./udiff_simple";
export { WholeFileCoder } from "./wholefile_coder";
export { SingleWholeFileFunctionCoder } from "./single_wholefile_func_coder";
export { WholeFileFunctionCoder } from "./wholefile_func_coder";

// Prompt classes
export { CoderPrompts } from "./base_prompts";
export { ArchitectPrompts } from "./architect_prompts";
export { AskPrompts } from "./ask_prompts";
export { ContextPrompts } from "./context_prompts";
export { EditBlockPrompts } from "./editblock_prompts";
export { EditBlockFencedPrompts } from "./editblock_fenced_prompts";
export { EditorDiffFencedPrompts } from "./editor_diff_fenced_prompts";
export { EditorEditBlockPrompts } from "./editor_editblock_prompts";
export { EditorWholePrompts } from "./editor_whole_prompts";
export { HelpPrompts } from "./help_prompts";
export { PatchPrompts } from "./patch_prompts";
export { UnifiedDiffPrompts } from "./udiff_prompts";
export { UnifiedDiffSimplePrompts } from "./udiff_simple_prompts";
export { WholeFilePrompts } from "./wholefile_prompts";
export { SingleWholeFileFunctionPrompts } from "./single_wholefile_func_prompts";
export { WholeFileFunctionPrompts } from "./wholefile_func_prompts";

// Utility modules
export * from "./chat_chunks";
export * from "./search_replace";
export * from "./shell";

// Legacy compatibility
export { BaseCoder as Coder } from "./BaseCoder";

// Re-export key interfaces and types for external use
export type {
  ChatMessage,
  Model,
  IO,
  GitRepo,
  RepoMap,
  CoderPrompts as ICoderPrompts,
  Commands,
  Analytics,
  ChatSummary,
  Linter,
  FileWatcher,
  WaitingSpinner,
} from "./BaseCoder";

export type { CoderConstructor } from "./registry";

// Export specific error types
export {
  UnknownEditFormat,
  MissingAPIKeyError,
  FinishReasonLength
} from "./BaseCoder";

export { DiffError, ActionType } from "./patch_coder";
export type {
  Chunk,
  PatchAction,
  PatchEdit
} from "./patch_coder";

// Default export for convenience
export { coderRegistry as default } from "./registry";
