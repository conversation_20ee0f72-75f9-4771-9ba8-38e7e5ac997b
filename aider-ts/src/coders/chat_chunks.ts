interface Message {
  content: string | Array<{ type: string; text: string; cache_control?: { type: string } }>;
  role?: string;
  [key: string]: any;
}

export class ChatChunks {
  system: Message[] = [];
  examples: Message[] = [];
  done: Message[] = [];
  repo: Message[] = [];
  readonly_files: Message[] = [];
  chat_files: Message[] = [];
  cur: Message[] = [];
  reminder: Message[] = [];

  all_messages(): Message[] {
    return [
      ...this.system,
      ...this.examples,
      ...this.readonly_files,
      ...this.repo,
      ...this.done,
      ...this.chat_files,
      ...this.cur,
      ...this.reminder
    ];
  }

  add_cache_control_headers(): void {
    if (this.examples.length > 0) {
      this.add_cache_control(this.examples);
    } else {
      this.add_cache_control(this.system);
    }

    if (this.repo.length > 0) {
      // this will mark both the readonly_files and repomap chunk as cacheable
      this.add_cache_control(this.repo);
    } else {
      // otherwise, just cache readonly_files if there are any
      this.add_cache_control(this.readonly_files);
    }

    this.add_cache_control(this.chat_files);
  }

  add_cache_control(messages: Message[]): void {
    if (!messages || messages.length === 0) {
      return;
    }

    let content = messages[messages.length - 1].content;
    if (typeof content === 'string') {
      content = {
        type: "text",
        text: content,
      };
    }
    
    if (Array.isArray(content)) {
      content[0].cache_control = { type: "ephemeral" };
    } else {
      (content as any).cache_control = { type: "ephemeral" };
      content = [content];
    }

    messages[messages.length - 1].content = content;
  }

  cacheable_messages(): Message[] {
    const messages = this.all_messages();
    for (let i = 0; i < messages.length; i++) {
      const message = messages[messages.length - 1 - i];
      if (Array.isArray(message.content) && 
          message.content[0] && 
          message.content[0].cache_control) {
        return messages.slice(0, messages.length - i);
      }
    }
    return messages;
  }
}
