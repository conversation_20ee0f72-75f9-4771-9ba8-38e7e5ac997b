import fs from 'fs';
import path from 'path';
import { EventEmitter } from 'events';

const __version__ = "0.0.1-ts";

// ============================================================================
// Interfaces
// ============================================================================

export interface ChatMessage {
    role: 'user' | 'assistant' | 'system';
    content: string | Array<{ type: string; text?: string; image_url?: any; cache_control?: any }>;
    function_call?: any;
    prefix?: boolean;
}

export interface Model {
    name: string;
    edit_format: string;
    editor_edit_format?: string;
    editor_model?: Model;
    weak_model: Model;
    info: {
        max_input_tokens?: number;
        max_output_tokens?: number;
        input_cost_per_token?: number;
        output_cost_per_token?: number;
        input_cost_per_token_cache_hit?: number;
        supports_vision?: boolean;
        supports_pdf_input?: boolean;
        supports_assistant_prefill?: boolean;
        [key: string]: any;
    };
    examples_as_sys_msg?: boolean;
    system_prompt_prefix?: string;
    use_system_prompt?: boolean;
    reminder?: string;
    streaming?: boolean;
    use_repo_map?: boolean;
    lazy?: boolean;
    overeager?: boolean;
    cache_control?: boolean;
    caches_by_default?: boolean;
    extra_params?: Record<string, any>;
    reasoning_tag?: string;
    get_thinking_tokens?(): number;
    get_reasoning_effort?(): string | null;
    get_repo_map_tokens?(): number;
    token_count?(text: string | ChatMessage[]): number;
    send_completion?(messages: ChatMessage[], functions?: any[], stream?: boolean, temperature?: number): Promise<[any, any]>;
}

export interface IO {
    pretty: boolean;
    encoding: string;
    chat_history_file: string;
    placeholder?: boolean;
    multiline_mode?: boolean;

    tool_warning(message: string): void;
    tool_output(message: string, bold?: boolean): void;
    tool_error(message: string, strip?: boolean): void;
    user_input(message: string): void;
    ai_output(message: string): void;
    assistant_output(message: string, pretty?: boolean): void;
    llm_started(): void;

    read_text(path: string): string | null;
    write_text(path: string, content: string): void;

    confirm_ask(message: string, subject?: string, group?: any, allow_never?: boolean, explicit_yes_required?: boolean): boolean;
    offer_url(url: string): void;

    get_input(root: string, files: string[], addable_files: string[], commands: any, read_only_files: Set<string>, edit_format?: string): Promise<string>;
    get_assistant_mdstream?(): any;

    add_to_input_history(command: string): void;
    log_llm_history(direction: string, content: string): void;
}

export interface GitRepo {
    root: string;
    repo: any;
    get_rel_repo_dir(): string;
    get_tracked_files(): string[];
    get_head_commit_sha(): string;
    path_in_repo(path: string): boolean;
    git_ignored_file(path: string): boolean;
    ignored_file(path: string): boolean;
    is_dirty(path?: string): boolean;
    commit(options: { fnames?: Set<string> | string[], context?: string, aider_edits?: boolean, coder?: BaseCoder }): [string, string] | null;
}

export interface RepoMap {
    max_map_tokens: number;
    refresh: string;
    map_mul_no_files: number;
    get_repo_map(chat_files: Set<string>, other_files: Set<string>, mentioned_fnames?: Set<string>, mentioned_idents?: Set<string>, force_refresh?: boolean): string | null;
}

export interface CoderPrompts {
    main_system: string;
    system_reminder: string;
    example_messages: ChatMessage[];
    files_content_prefix: string;
    files_content_assistant_reply: string;
    files_no_full_files: string;
    files_no_full_files_with_repo_map: string;
    files_no_full_files_with_repo_map_reply: string;
    repo_content_prefix: string;
    read_only_files_prefix: string;
    files_content_gpt_edits: string;
    files_content_gpt_edits_no_repo: string;
    files_content_gpt_no_edits: string;
    lazy_prompt: string;
    overeager_prompt: string;
    shell_cmd_prompt: string;
    shell_cmd_reminder: string;
    go_ahead_tip: string;
    [key: string]: any;
}

export interface Commands {
    io: IO;
    coder: BaseCoder;
    clone(): Commands;
    is_command(input: string): boolean;
    run(input: string): string | null;
    cmd_copy_context(): void;
    cmd_test(test_cmd?: string): string | null;
    cmd_diff(): void;
    cmd_web(url: string, return_content?: boolean): string;
}

export interface Analytics {
    event(name: string, properties?: Record<string, any>): void;
    enable(): void;
}

export interface ChatSummary {
    too_big(messages: ChatMessage[]): boolean;
    summarize(messages: ChatMessage[]): ChatMessage[];
    summarize_all(messages: ChatMessage[]): ChatMessage[];
}

export interface Linter {
    lint(path: string): string;
    set_linter(lang: string, cmd: string): void;
}

export interface FileWatcher {
    coder?: BaseCoder;
}

export interface WaitingSpinner {
    start(): void;
    stop(): void;
}

// ============================================================================
// Chat Chunks Helper Class
// ============================================================================

export class ChatChunks {
    system: ChatMessage[] = [];
    examples: ChatMessage[] = [];
    done: ChatMessage[] = [];
    repo: ChatMessage[] = [];
    readonly_files: ChatMessage[] = [];
    chat_files: ChatMessage[] = [];
    cur: ChatMessage[] = [];
    reminder: ChatMessage[] = [];

    all_messages(): ChatMessage[] {
        return [
            ...this.system,
            ...this.examples,
            ...this.readonly_files,
            ...this.repo,
            ...this.done,
            ...this.chat_files,
            ...this.cur,
            ...this.reminder
        ];
    }

    add_cache_control_headers(): void {
        if (this.examples.length > 0) {
            this.add_cache_control(this.examples);
        } else {
            this.add_cache_control(this.system);
        }

        if (this.repo.length > 0) {
            this.add_cache_control(this.repo);
        } else {
            this.add_cache_control(this.readonly_files);
        }

        this.add_cache_control(this.chat_files);
    }

    add_cache_control(messages: ChatMessage[]): void {
        if (!messages || messages.length === 0) return;

        const lastMessage = messages[messages.length - 1];
        let content = lastMessage.content;

        if (typeof content === 'string') {
            content = [{
                type: "text",
                text: content,
                cache_control: { type: "ephemeral" }
            }];
        } else if (Array.isArray(content) && content.length > 0) {
            content[0].cache_control = { type: "ephemeral" };
        }

        lastMessage.content = content;
    }

    cacheable_messages(): ChatMessage[] {
        const messages = this.all_messages();
        for (let i = messages.length - 1; i >= 0; i--) {
            const message = messages[i];
            if (Array.isArray(message.content) &&
                message.content[0] &&
                message.content[0].cache_control) {
                return messages.slice(0, i + 1);
            }
        }
        return messages;
    }
}

// ============================================================================
// Exceptions
// ============================================================================

export class UnknownEditFormat extends Error {
    edit_format: string;
    valid_formats: string[];

    constructor(edit_format: string, valid_formats: string[]) {
        super(`Unknown edit format ${edit_format}. Valid formats are: ${valid_formats.join(', ')}`);
        this.edit_format = edit_format;
        this.valid_formats = valid_formats;
    }
}

export class MissingAPIKeyError extends Error {}
export class FinishReasonLength extends Error {}

// ============================================================================
// Main BaseCoder Class
// ============================================================================

export abstract class BaseCoder extends EventEmitter {
    // Core properties
    abs_fnames: Set<string> = new Set();
    abs_read_only_fnames: Set<string> = new Set();
    repo: GitRepo | null = null;
    last_aider_commit_hash: string | null = null;
    aider_edited_files: Set<string> | null = null;
    last_asked_for_commit_time: number = 0;
    repo_map: RepoMap | null = null;
    functions: any[] | null = null;
    num_exhausted_context_windows: number = 0;
    num_malformed_responses: number = 0;
    last_keyboard_interrupt: Date | null = null;
    num_reflections: number = 0;
    max_reflections: number = 3;
    edit_format: string | null = null;
    yield_stream: boolean = false;
    temperature: number | null = null;
    auto_lint: boolean = true;
    auto_test: boolean = false;
    test_cmd: string | null = null;
    lint_outcome: boolean | null = null;
    test_outcome: boolean | null = null;
    multi_response_content: string = "";
    partial_response_content: string = "";
    partial_response_function_call: Record<string, any> = {};
    commit_before_message: string[] = [];
    message_cost: number = 0.0;
    add_cache_headers: boolean = false;
    cache_warming_thread: any = null;
    num_cache_warming_pings: number = 0;
    suggest_shell_commands: boolean = true;
    detect_urls: boolean = true;
    ignore_mentions: Set<string> = new Set();
    chat_language: string | null = null;
    commit_language: string | null = null;
    file_watcher: FileWatcher | null = null;

    // Abstract property - must be implemented by subclasses
    abstract gpt_prompts: CoderPrompts;

    // Core dependencies
    main_model: Model;
    io: IO;

    // Additional properties
    root: string;
    verbose: boolean = false;
    show_diffs: boolean = false;
    auto_commits: boolean = true;
    dirty_commits: boolean = true;
    dry_run: boolean = false;
    stream: boolean = true;
    cur_messages: ChatMessage[] = [];
    done_messages: ChatMessage[] = [];
    aider_commit_hashes: Set<string> = new Set();
    commands: Commands;
    summarizer: ChatSummary;
    summarizer_thread: any = null;
    summarized_done_messages: ChatMessage[] = [];
    summarizing_messages: ChatMessage[] | null = null;
    linter: Linter;
    total_cost: number = 0.0;
    total_tokens_sent: number = 0;
    total_tokens_received: number = 0;
    message_tokens_sent: number = 0;
    message_tokens_received: number = 0;
    usage_report: string | null = null;

    // Internal properties
    rejected_urls: Set<string> = new Set();
    abs_root_path_cache: Record<string, string> = {};
    chat_completion_call_hashes: string[] = [];
    chat_completion_response_hashes: string[] = [];
    need_commit_before_edits: Set<string> = new Set();
    auto_copy_context: boolean = false;
    auto_accept_architect: boolean = true;
    original_kwargs: Record<string, any> = {};
    ok_to_warm_cache: boolean = false;
    reflected_message: string | null = null;
    shell_commands: string[] = [];
    waiting_spinner: WaitingSpinner | null = null;
    mdstream: any = null;

    // Fence handling
    fences: [string, string][] = [
        ['```', '```'],
        ['````', '````'],
        ['<source>', '</source>'],
        ['<code>', '</code>'],
        ['<pre>', '</pre>'],
        ['<codeblock>', '</codeblock>'],
        ['<sourcecode>', '</sourcecode>']
    ];
    fence: [string, string] = this.fences[0];

    constructor(
        main_model: Model,
        io: IO,
        options: {
            repo?: GitRepo | null;
            fnames?: string[];
            read_only_fnames?: string[];
            show_diffs?: boolean;
            auto_commits?: boolean;
            dirty_commits?: boolean;
            dry_run?: boolean;
            verbose?: boolean;
            stream?: boolean;
            cur_messages?: ChatMessage[];
            done_messages?: ChatMessage[];
            auto_lint?: boolean;
            auto_test?: boolean;
            test_cmd?: string;
            aider_commit_hashes?: Set<string>;
            commands?: Commands;
            summarizer?: ChatSummary;
            total_cost?: number;
            cache_prompts?: boolean;
            num_cache_warming_pings?: number;
            suggest_shell_commands?: boolean;
            chat_language?: string;
            commit_language?: string;
            detect_urls?: boolean;
            ignore_mentions?: Set<string>;
            total_tokens_sent?: number;
            total_tokens_received?: number;
            file_watcher?: FileWatcher;
            auto_copy_context?: boolean;
            auto_accept_architect?: boolean;
            [key: string]: any;
        } = {}
    ) {
        super();

        this.main_model = main_model;
        this.io = io;
        this.repo = options.repo || null;
        this.show_diffs = options.show_diffs || false;
        this.auto_commits = options.auto_commits !== undefined ? options.auto_commits : true;
        this.dirty_commits = options.dirty_commits !== undefined ? options.dirty_commits : true;
        this.dry_run = options.dry_run || false;
        this.verbose = options.verbose || false;
        this.stream = options.stream !== undefined ? options.stream : true;
        this.cur_messages = options.cur_messages || [];
        this.done_messages = options.done_messages || [];
        this.auto_lint = options.auto_lint !== undefined ? options.auto_lint : true;
        this.auto_test = options.auto_test || false;
        this.test_cmd = options.test_cmd || null;
        this.aider_commit_hashes = options.aider_commit_hashes || new Set();
        this.total_cost = options.total_cost || 0.0;
        this.suggest_shell_commands = options.suggest_shell_commands !== undefined ? options.suggest_shell_commands : true;
        this.chat_language = options.chat_language || null;
        this.commit_language = options.commit_language || null;
        this.detect_urls = options.detect_urls !== undefined ? options.detect_urls : true;
        this.ignore_mentions = options.ignore_mentions || new Set();
        this.total_tokens_sent = options.total_tokens_sent || 0;
        this.total_tokens_received = options.total_tokens_received || 0;
        this.file_watcher = options.file_watcher || null;
        this.auto_copy_context = options.auto_copy_context || false;
        this.auto_accept_architect = options.auto_accept_architect !== undefined ? options.auto_accept_architect : true;
        this.num_cache_warming_pings = options.num_cache_warming_pings || 0;

        if (options.cache_prompts && this.main_model.cache_control) {
            this.add_cache_headers = true;
        }

        // Set up root directory
        if (this.repo) {
            this.root = this.repo.root;
        } else {
            this.root = process.cwd();
        }

        // Initialize file watcher
        if (this.file_watcher) {
            this.file_watcher.coder = this;
        }

        // Process initial file names
        const fnames = options.fnames || [];
        for (const fname of fnames) {
            this.add_file(fname);
        }

        // Process read-only files
        const read_only_fnames = options.read_only_fnames || [];
        for (const fname of read_only_fnames) {
            const abs_fname = this.abs_root_path(fname);
            if (fs.existsSync(abs_fname)) {
                this.abs_read_only_fnames.add(abs_fname);
            } else {
                this.io.tool_warning(`Error: Read-only file ${fname} does not exist. Skipping.`);
            }
        }

        // Initialize dependencies
        this.commands = options.commands || this.create_commands();
        this.summarizer = options.summarizer || this.create_summarizer();
        this.linter = this.create_linter();

        // Set up streaming
        this.stream = this.stream && this.main_model.streaming;

        this.original_kwargs = { ...options };
    }

    // ========================================================================
    // Factory Methods
    // ========================================================================

    static create(options: {
        main_model?: Model;
        edit_format?: string;
        io?: IO;
        from_coder?: BaseCoder;
        summarize_from_coder?: boolean;
        [key: string]: any;
    } = {}): BaseCoder {
        const main_model = options.main_model || (options.from_coder?.main_model);
        if (!main_model) {
            throw new Error("main_model is required");
        }

        const io = options.io || options.from_coder?.io;
        if (!io) {
            throw new Error("io is required");
        }

        let edit_format = options.edit_format;
        if (edit_format === "code") {
            edit_format = null;
        }
        if (!edit_format) {
            edit_format = options.from_coder?.edit_format || main_model.edit_format;
        }

        // Import all coder classes dynamically
        const coders = BaseCoder.get_all_coders();

        for (const CoderClass of coders) {
            if ('edit_format' in CoderClass && (CoderClass as any).edit_format === edit_format) {
                return new (CoderClass as any)(main_model, io, options);
            }
        }

        const valid_formats = coders
            .filter(c => 'edit_format' in c && (c as any).edit_format !== null)
            .map(c => (c as any).edit_format);

        throw new UnknownEditFormat(edit_format, valid_formats);
    }

    // This should be overridden by the registry system
    static get_all_coders(): (typeof BaseCoder)[] {
        // This is a placeholder - in a real implementation, this would
        // return all registered coder classes
        return [];
    }

    clone(options: Record<string, any> = {}): BaseCoder {
        const new_options = { ...this.original_kwargs, ...options, from_coder: this };
        return BaseCoder.create(new_options);
    }

    // ========================================================================
    // Abstract Methods (must be implemented by subclasses)
    // ========================================================================

    abstract get_edits(): any[];
    abstract apply_edits(edits: any[]): void;

    // ========================================================================
    // Core Methods
    // ========================================================================

    add_file(fname: string): void {
        const file_path = path.resolve(fname);

        // Check if file exists, create if needed
        if (!fs.existsSync(file_path)) {
            try {
                fs.writeFileSync(file_path, '', 'utf8');
                this.io.tool_output(`Creating empty file ${fname}`);
            } catch (error) {
                this.io.tool_warning(`Can not create ${fname}, skipping.`);
                return;
            }
        }

        // Check if it's a regular file
        const stats = fs.statSync(file_path);
        if (!stats.isFile()) {
            this.io.tool_warning(`Skipping ${fname} that is not a normal file.`);
            return;
        }

        this.abs_fnames.add(file_path);
        this.check_added_files();
    }

    check_added_files(): void {
        const warn_number_of_files = 4;
        const warn_number_of_tokens = 20 * 1024;

        const num_files = this.abs_fnames.size;
        if (num_files < warn_number_of_files) {
            return;
        }

        let tokens = 0;
        for (const fname of this.abs_fnames) {
            if (this.is_image_file(fname)) {
                continue;
            }
            const content = this.io.read_text(fname);
            if (content && this.main_model.token_count) {
                tokens += this.main_model.token_count(content);
            }
        }

        if (tokens < warn_number_of_tokens) {
            return;
        }

        this.io.tool_warning("Warning: it's best to only add files that need changes to the chat.");
    }

    abs_root_path(file_path: string): string {
        if (this.abs_root_path_cache[file_path]) {
            return this.abs_root_path_cache[file_path];
        }

        const resolved = path.resolve(this.root, file_path);
        this.abs_root_path_cache[file_path] = resolved;
        return resolved;
    }

    get_rel_fname(fname: string): string {
        return path.relative(this.root, fname);
    }

    get_inchat_relative_files(): string[] {
        return Array.from(this.abs_fnames).map(fname => this.get_rel_fname(fname)).sort();
    }

    get_all_relative_files(): string[] {
        if (this.repo) {
            return this.repo.get_tracked_files().sort();
        } else {
            return this.get_inchat_relative_files();
        }
    }

    get_addable_relative_files(): string[] {
        const all_files = new Set(this.get_all_relative_files());
        const inchat_files = new Set(this.get_inchat_relative_files());
        const read_only_files = new Set(Array.from(this.abs_read_only_fnames).map(f => this.get_rel_fname(f)));

        return Array.from(all_files).filter(f => !inchat_files.has(f) && !read_only_files.has(f));
    }

    // ========================================================================
    // Message and Chat Management
    // ========================================================================

    async run(options: { with_message?: string; preproc?: boolean } = {}): Promise<string | void> {
        try {
            if (options.with_message) {
                this.io.user_input(options.with_message);
                await this.run_one(options.with_message, options.preproc !== false);
                return this.partial_response_content;
            }

            while (true) {
                try {
                    if (!this.io.placeholder) {
                        this.copy_context();
                    }
                    const user_message = await this.get_input();
                    await this.run_one(user_message, options.preproc !== false);
                    this.show_undo_hint();
                } catch (error) {
                    if (error instanceof Error && error.message === 'KeyboardInterrupt') {
                        this.keyboard_interrupt();
                    } else {
                        throw error;
                    }
                }
            }
        } catch (error) {
            if (error instanceof Error && error.message === 'EOFError') {
                return;
            }
            throw error;
        }
    }

    async run_one(user_message: string, preproc: boolean): Promise<void> {
        this.init_before_message();

        let message = user_message;
        if (preproc) {
            const processed = this.preproc_user_input(user_message);
            if (processed !== undefined) {
                message = processed;
            }
        }

        while (message) {
            this.reflected_message = null;
            await this.send_message(message);

            if (!this.reflected_message) {
                break;
            }

            if (this.num_reflections >= this.max_reflections) {
                this.io.tool_warning(`Only ${this.max_reflections} reflections allowed, stopping.`);
                return;
            }

            this.num_reflections += 1;
            message = this.reflected_message;
        }
    }

    init_before_message(): void {
        this.aider_edited_files = new Set();
        this.reflected_message = null;
        this.num_reflections = 0;
        this.lint_outcome = null;
        this.test_outcome = null;
        this.shell_commands = [];
        this.message_cost = 0;

        if (this.repo) {
            this.commit_before_message.push(this.repo.get_head_commit_sha());
        }
    }

    preproc_user_input(inp: string): string | undefined {
        if (!inp) {
            return;
        }

        if (this.commands.is_command(inp)) {
            return this.commands.run(inp) || undefined;
        }

        this.check_for_file_mentions(inp);
        inp = this.check_for_urls(inp);

        return inp;
    }

    async send_message(inp: string): Promise<void> {
        this.emit('message_send_starting');
        this.io.llm_started();

        this.cur_messages.push({
            role: "user",
            content: inp,
        });

        const chunks = this.format_messages();
        const messages = chunks.all_messages();

        if (!this.check_tokens(messages)) {
            return;
        }

        if (this.verbose) {
            console.log("Messages:", JSON.stringify(messages, null, 2));
        }

        this.multi_response_content = "";

        if (this.show_pretty()) {
            // this.waiting_spinner = new WaitingSpinner("Waiting for " + this.main_model.name);
            // this.waiting_spinner.start();
            if (this.stream) {
                this.mdstream = this.io.get_assistant_mdstream?.();
            }
        }

        try {
            if (this.main_model.send_completion) {
                const [hash_object, completion] = await this.main_model.send_completion(
                    messages,
                    this.functions,
                    this.stream,
                    this.temperature
                );

                this.chat_completion_call_hashes.push(hash_object);

                if (this.stream) {
                    await this.show_send_output_stream(completion);
                } else {
                    this.show_send_output(completion);
                }

                this.calculate_and_show_tokens_and_cost(messages, completion);
            }
        } catch (error) {
            this.io.tool_error(`Error sending message: ${error}`);
            return;
        } finally {
            this.stop_waiting_spinner();
            this.partial_response_content = this.get_multi_response_content_in_progress(true);
            this.multi_response_content = "";

            if (this.mdstream) {
                this.live_incremental_response(true);
                this.mdstream = null;
            }
        }

        this.io.tool_output("");
        this.show_usage_report();
        this.add_assistant_reply_to_cur_messages();

        // Handle reply completion
        try {
            await this.reply_completed();
        } catch (error) {
            if (error instanceof Error && error.message === 'KeyboardInterrupt') {
                this.keyboard_interrupt();
                return;
            }
            throw error;
        }

        // Apply edits
        const edited = this.apply_updates();

        if (edited.size > 0) {
            this.aider_edited_files = new Set([...this.aider_edited_files || [], ...edited]);
            const saved_message = this.auto_commit(edited);
            this.move_back_cur_messages(saved_message);
        }

        // Auto-lint if needed
        if (edited.size > 0 && this.auto_lint) {
            const lint_errors = this.lint_edited(edited);
            if (lint_errors) {
                this.lint_outcome = false;
                const ok = this.io.confirm_ask("Attempt to fix lint errors?");
                if (ok) {
                    this.reflected_message = lint_errors;
                    return;
                }
            } else {
                this.lint_outcome = true;
            }
        }

        // Run shell commands
        const shared_output = this.run_shell_commands();
        if (shared_output) {
            this.cur_messages.push(
                { role: "user", content: shared_output },
                { role: "assistant", content: "Ok" }
            );
        }

        // Auto-test if needed
        if (edited.size > 0 && this.auto_test) {
            const test_errors = this.commands.cmd_test(this.test_cmd);
            if (test_errors) {
                this.test_outcome = false;
                const ok = this.io.confirm_ask("Attempt to fix test errors?");
                if (ok) {
                    this.reflected_message = test_errors;
                    return;
                }
            } else {
                this.test_outcome = true;
            }
        }
    }

    // ========================================================================
    // Message Formatting
    // ========================================================================

    format_messages(): ChatChunks {
        this.choose_fence();
        const chunks = new ChatChunks();

        // System message
        const main_sys = this.fmt_system_prompt(this.gpt_prompts.main_system);
        if (this.main_model.use_system_prompt) {
            chunks.system = [{ role: "system", content: main_sys }];
        } else {
            chunks.system = [
                { role: "user", content: main_sys },
                { role: "assistant", content: "Ok." }
            ];
        }

        // Example messages
        for (const msg of this.gpt_prompts.example_messages) {
            chunks.examples.push({
                role: msg.role,
                content: this.fmt_system_prompt(msg.content as string)
            });
        }

        // Done messages (chat history)
        this.summarize_end();
        chunks.done = this.done_messages;

        // Repo and file messages
        chunks.repo = this.get_repo_messages();
        chunks.readonly_files = this.get_readonly_files_messages();
        chunks.chat_files = this.get_chat_files_messages();

        // Current messages
        chunks.cur = [...this.cur_messages];

        // System reminder
        if (this.gpt_prompts.system_reminder) {
            const reminder_message = [{
                role: "system" as const,
                content: this.fmt_system_prompt(this.gpt_prompts.system_reminder)
            }];
            chunks.reminder = reminder_message;
        }

        // Add cache headers if needed
        if (this.add_cache_headers) {
            chunks.add_cache_control_headers();
        }

        return chunks;
    }

    fmt_system_prompt(prompt: string): string {
        const final_reminders: string[] = [];
        if (this.main_model.lazy) {
            final_reminders.push(this.gpt_prompts.lazy_prompt);
        }
        if (this.main_model.overeager) {
            final_reminders.push(this.gpt_prompts.overeager_prompt);
        }

        const user_lang = this.get_user_language();
        if (user_lang) {
            final_reminders.push(`Reply in ${user_lang}.\n`);
        }

        const platform_text = this.get_platform_info();
        const language = user_lang || "the same language they are using";

        let quad_backtick_reminder = "";
        if (this.fence[0] === "````") {
            quad_backtick_reminder = "\nIMPORTANT: Use *quadruple* backticks ```` as fences, not triple backticks!\n";
        }

        const final_reminders_text = final_reminders.join("\n\n");

        return prompt
            .replace(/\{fence\}/g, `${this.fence[0]}...${this.fence[1]}`)
            .replace(/\{quad_backtick_reminder\}/g, quad_backtick_reminder)
            .replace(/\{final_reminders\}/g, final_reminders_text)
            .replace(/\{platform\}/g, platform_text)
            .replace(/\{shell_cmd_prompt\}/g, this.gpt_prompts.shell_cmd_prompt || "")
            .replace(/\{shell_cmd_reminder\}/g, this.gpt_prompts.shell_cmd_reminder || "")
            .replace(/\{go_ahead_tip\}/g, this.gpt_prompts.go_ahead_tip || "")
            .replace(/\{language\}/g, language);
    }

    get_user_language(): string | null {
        if (this.chat_language) {
            return this.normalize_language(this.chat_language);
        }
        // Simple fallback - in a real implementation this would check system locale
        return null;
    }

    normalize_language(lang_code: string): string | null {
        if (!lang_code) return null;

        const fallback: Record<string, string> = {
            "en": "English",
            "fr": "French",
            "es": "Spanish",
            "de": "German",
            "it": "Italian",
            "pt": "Portuguese",
            "zh": "Chinese",
            "ja": "Japanese",
            "ko": "Korean",
            "ru": "Russian",
        };

        const primary_lang_code = lang_code.replace("-", "_").split("_")[0].toLowerCase();
        return fallback[primary_lang_code] || lang_code;
    }

    get_platform_info(): string {
        let platform_text = "";
        try {
            platform_text = `- Platform: ${process.platform}\n`;
        } catch {
            platform_text = "- Platform information unavailable\n";
        }

        const shell_var = process.platform === "win32" ? "COMSPEC" : "SHELL";
        const shell_val = process.env[shell_var];
        platform_text += `- Shell: ${shell_var}=${shell_val}\n`;

        const user_lang = this.get_user_language();
        if (user_lang) {
            platform_text += `- Language: ${user_lang}\n`;
        }

        const dt = new Date().toISOString().split('T')[0];
        platform_text += `- Current date: ${dt}\n`;

        if (this.repo) {
            platform_text += "- The user is operating inside a git repository\n";
        }

        return platform_text;
    }

    get_repo_messages(): ChatMessage[] {
        // Placeholder - would implement repo map functionality
        return [];
    }

    get_readonly_files_messages(): ChatMessage[] {
        const readonly_messages: ChatMessage[] = [];

        // Handle non-image files
        const read_only_content = this.get_read_only_files_content();
        if (read_only_content) {
            readonly_messages.push(
                {
                    role: "user",
                    content: this.gpt_prompts.read_only_files_prefix + read_only_content
                },
                {
                    role: "assistant",
                    content: "Ok, I will use these files as references."
                }
            );
        }

        return readonly_messages;
    }

    get_read_only_files_content(): string {
        let prompt = "";
        for (const fname of this.abs_read_only_fnames) {
            const content = this.io.read_text(fname);
            if (content !== null && !this.is_image_file(fname)) {
                const relative_fname = this.get_rel_fname(fname);
                prompt += `\n${relative_fname}\n${this.fence[0]}\n${content}${this.fence[1]}\n`;
            }
        }
        return prompt;
    }

    get_chat_files_messages(): ChatMessage[] {
        const chat_files_messages: ChatMessage[] = [];

        if (this.abs_fnames.size > 0) {
            const files_content = this.gpt_prompts.files_content_prefix + this.get_files_content();
            const files_reply = this.gpt_prompts.files_content_assistant_reply;

            chat_files_messages.push(
                { role: "user", content: files_content },
                { role: "assistant", content: files_reply }
            );
        } else {
            const files_content = this.gpt_prompts.files_no_full_files;
            chat_files_messages.push(
                { role: "user", content: files_content },
                { role: "assistant", content: "Ok." }
            );
        }

        return chat_files_messages;
    }

    get_files_content(): string {
        let prompt = "";
        for (const fname of this.abs_fnames) {
            const content = this.io.read_text(fname);
            if (content !== null && !this.is_image_file(fname)) {
                const relative_fname = this.get_rel_fname(fname);
                prompt += `\n${relative_fname}\n${this.fence[0]}\n${content}${this.fence[1]}\n`;
            }
        }
        return prompt;
    }

    choose_fence(): void {
        let all_content = "";
        for (const fname of this.abs_fnames) {
            const content = this.io.read_text(fname);
            if (content !== null) {
                all_content += content + "\n";
            }
        }
        for (const fname of this.abs_read_only_fnames) {
            const content = this.io.read_text(fname);
            if (content !== null) {
                all_content += content + "\n";
            }
        }

        const lines = all_content.split('\n');
        let good = false;

        for (const [fence_open, fence_close] of this.fences) {
            if (!lines.some(line => line.startsWith(fence_open) || line.startsWith(fence_close))) {
                this.fence = [fence_open, fence_close];
                good = true;
                break;
            }
        }

        if (!good) {
            this.fence = this.fences[0];
            this.io.tool_warning(
                `Unable to find a fencing strategy! Falling back to: ${this.fence[0]}...${this.fence[1]}`
            );
        }
    }

    is_image_file(fname: string): boolean {
        const ext = path.extname(fname).toLowerCase();
        return ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp', '.pdf'].includes(ext);
    }

    check_tokens(messages: ChatMessage[]): boolean {
        if (!this.main_model.token_count) {
            return true;
        }

        const input_tokens = this.main_model.token_count(messages);
        const max_input_tokens = this.main_model.info.max_input_tokens || 0;

        if (max_input_tokens && input_tokens >= max_input_tokens) {
            this.io.tool_error(
                `Your estimated chat context of ${input_tokens.toLocaleString()} tokens exceeds the ` +
                `${max_input_tokens.toLocaleString()} token limit for ${this.main_model.name}!`
            );
            this.io.tool_output("To reduce the chat context:");
            this.io.tool_output("- Use /drop to remove unneeded files from the chat");
            this.io.tool_output("- Use /clear to clear the chat history");
            this.io.tool_output("- Break your code into smaller files");

            return this.io.confirm_ask("Try to proceed anyway?");
        }

        return true;
    }

    show_pretty(): boolean {
        if (!this.io.pretty) {
            return false;
        }
        // Only show pretty output if fences are normal triple-backtick
        return this.fence[0].startsWith("`");
    }

    show_send_output(completion: any): void {
        this.stop_waiting_spinner();

        if (this.verbose) {
            console.log("Completion:", completion);
        }

        if (!completion.choices || completion.choices.length === 0) {
            this.io.tool_error(String(completion));
            return;
        }

        try {
            this.partial_response_content = completion.choices[0].message?.content || "";

            if (completion.choices[0].message?.tool_calls) {
                this.partial_response_function_call = completion.choices[0].message.tool_calls[0].function;
            }
        } catch (error) {
            this.io.tool_error(`Error processing completion: ${error}`);
            return;
        }

        const show_resp = this.render_incremental_response(true);
        this.io.assistant_output(show_resp, this.show_pretty());

        if (completion.choices[0]?.finish_reason === "length") {
            throw new FinishReasonLength();
        }
    }

    async show_send_output_stream(completion: any): Promise<void> {
        let received_content = false;

        for await (const chunk of completion) {
            if (!chunk.choices || chunk.choices.length === 0) {
                continue;
            }

            if (chunk.choices[0]?.finish_reason === "length") {
                throw new FinishReasonLength();
            }

            let text = "";

            try {
                const content = chunk.choices[0]?.delta?.content;
                if (content) {
                    text += content;
                    received_content = true;
                }
            } catch (error) {
                // Continue processing
            }

            if (received_content) {
                this.stop_waiting_spinner();
            }

            this.partial_response_content += text;

            if (this.show_pretty()) {
                this.live_incremental_response(false);
            } else if (text) {
                try {
                    process.stdout.write(text);
                } catch (error) {
                    // Handle encoding errors
                    process.stdout.write(text.replace(/[^\x00-\x7F]/g, "?"));
                }
            }
        }

        if (!received_content) {
            this.io.tool_warning("Empty response received from LLM. Check your provider account?");
        }
    }

    live_incremental_response(final: boolean): void {
        const show_resp = this.render_incremental_response(final);
        if (this.mdstream && this.mdstream.update) {
            this.mdstream.update(show_resp, final);
        }
    }

    render_incremental_response(final: boolean): string {
        return this.get_multi_response_content_in_progress();
    }

    get_multi_response_content_in_progress(final: boolean = false): string {
        const cur = this.multi_response_content || "";
        let new_content = this.partial_response_content || "";

        if (new_content.endsWith(' ') && !final) {
            new_content = new_content.trimEnd();
        }

        return cur + new_content;
    }

    stop_waiting_spinner(): void {
        if (this.waiting_spinner) {
            try {
                this.waiting_spinner.stop();
            } finally {
                this.waiting_spinner = null;
            }
        }
    }

    calculate_and_show_tokens_and_cost(messages: ChatMessage[], completion: any): void {
        let prompt_tokens = 0;
        let completion_tokens = 0;

        if (completion?.usage) {
            prompt_tokens = completion.usage.prompt_tokens || 0;
            completion_tokens = completion.usage.completion_tokens || 0;
        } else if (this.main_model.token_count) {
            prompt_tokens = this.main_model.token_count(messages);
            completion_tokens = this.main_model.token_count(this.partial_response_content);
        }

        this.message_tokens_sent += prompt_tokens;
        this.message_tokens_received += completion_tokens;

        let tokens_report = `Tokens: ${this.format_tokens(this.message_tokens_sent)} sent, ${this.format_tokens(this.message_tokens_received)} received.`;

        if (this.main_model.info.input_cost_per_token) {
            const cost = this.compute_costs_from_tokens(prompt_tokens, completion_tokens, 0, 0);
            this.total_cost += cost;
            this.message_cost += cost;

            const cost_report = `Cost: $${this.format_cost(this.message_cost)} message, $${this.format_cost(this.total_cost)} session.`;
            tokens_report += " " + cost_report;
        }

        this.usage_report = tokens_report;
    }

    format_tokens(value: number): string {
        return value.toLocaleString();
    }

    format_cost(value: number): string {
        if (value === 0) return "0.00";
        const magnitude = Math.abs(value);
        if (magnitude >= 0.01) {
            return value.toFixed(2);
        } else {
            return value.toFixed(Math.max(2, 2 - Math.floor(Math.log10(magnitude))));
        }
    }

    compute_costs_from_tokens(prompt_tokens: number, completion_tokens: number, cache_write_tokens: number, cache_hit_tokens: number): number {
        let cost = 0;
        const input_cost_per_token = this.main_model.info.input_cost_per_token || 0;
        const output_cost_per_token = this.main_model.info.output_cost_per_token || 0;

        cost += prompt_tokens * input_cost_per_token;
        cost += completion_tokens * output_cost_per_token;

        return cost;
    }

    show_usage_report(): void {
        if (!this.usage_report) {
            return;
        }

        this.total_tokens_sent += this.message_tokens_sent;
        this.total_tokens_received += this.message_tokens_received;

        this.io.tool_output(this.usage_report);

        this.emit('message_send', {
            main_model: this.main_model,
            edit_format: this.edit_format,
            prompt_tokens: this.message_tokens_sent,
            completion_tokens: this.message_tokens_received,
            total_tokens: this.message_tokens_sent + this.message_tokens_received,
            cost: this.message_cost,
            total_cost: this.total_cost
        });

        this.message_cost = 0.0;
        this.message_tokens_sent = 0;
        this.message_tokens_received = 0;
    }

    add_assistant_reply_to_cur_messages(): void {
        if (this.partial_response_content) {
            this.cur_messages.push({
                role: "assistant",
                content: this.partial_response_content
            });
        }
        if (this.partial_response_function_call && Object.keys(this.partial_response_function_call).length > 0) {
            this.cur_messages.push({
                role: "assistant",
                content: null,
                function_call: this.partial_response_function_call
            });
        }
    }

    // ========================================================================
    // Edit Application
    // ========================================================================

    apply_updates(): Set<string> {
        const edited = new Set<string>();
        try {
            const edits = this.get_edits();
            const prepared_edits = this.prepare_to_edit(edits);

            for (const edit of prepared_edits) {
                if (Array.isArray(edit) && edit.length >= 1) {
                    edited.add(edit[0]);
                }
            }

            this.apply_edits(prepared_edits);
        } catch (error) {
            this.num_malformed_responses += 1;
            this.io.tool_error("The LLM did not conform to the edit format.");
            this.io.tool_output(String(error));
            this.reflected_message = String(error);
            return edited;
        }

        for (const path of edited) {
            if (this.dry_run) {
                this.io.tool_output(`Did not apply edit to ${path} (--dry-run)`);
            } else {
                this.io.tool_output(`Applied edit to ${path}`);
            }
        }

        return edited;
    }

    prepare_to_edit(edits: any[]): any[] {
        const res: any[] = [];
        const seen = new Map<string, boolean>();

        this.need_commit_before_edits = new Set();

        for (const edit of edits) {
            let path: string | null = null;
            if (Array.isArray(edit) && edit.length > 0) {
                path = edit[0];
            }

            if (path === null) {
                res.push(edit);
                continue;
            }

            let allowed: boolean;
            if (seen.has(path)) {
                allowed = seen.get(path)!;
            } else {
                allowed = this.allowed_to_edit(path);
                seen.set(path, allowed);
            }

            if (allowed) {
                res.push(edit);
            }
        }

        this.dirty_commit();
        this.need_commit_before_edits = new Set();

        return res;
    }

    allowed_to_edit(path: string): boolean {
        const full_path = this.abs_root_path(path);

        if (this.abs_fnames.has(full_path)) {
            this.check_for_dirty_commit(path);
            return true;
        }

        if (!fs.existsSync(full_path)) {
            if (!this.io.confirm_ask("Create new file?", path)) {
                this.io.tool_output(`Skipping edits to ${path}`);
                return false;
            }

            if (!this.dry_run) {
                try {
                    fs.writeFileSync(full_path, '', 'utf8');
                } catch (error) {
                    this.io.tool_error(`Unable to create ${path}, skipping edits.`);
                    return false;
                }
            }

            this.abs_fnames.add(full_path);
            this.check_added_files();
            return true;
        }

        if (!this.io.confirm_ask("Allow edits to file that has not been added to the chat?", path)) {
            this.io.tool_output(`Skipping edits to ${path}`);
            return false;
        }

        this.abs_fnames.add(full_path);
        this.check_added_files();
        this.check_for_dirty_commit(path);

        return true;
    }

    check_for_dirty_commit(path: string): void {
        if (!this.repo || !this.dirty_commits) {
            return;
        }
        if (!this.repo.is_dirty(path)) {
            return;
        }

        this.io.tool_output(`Committing ${path} before applying edits.`);
        this.need_commit_before_edits.add(path);
    }

    dirty_commit(): void {
        if (this.need_commit_before_edits.size === 0) {
            return;
        }
        if (!this.dirty_commits || !this.repo) {
            return;
        }

        this.repo.commit({
            fnames: this.need_commit_before_edits,
            coder: this
        });
    }

    auto_commit(edited: Set<string>): string | null {
        if (!this.repo || !this.auto_commits || this.dry_run) {
            return null;
        }

        const context = this.get_context_from_history(this.cur_messages);

        try {
            const res = this.repo.commit({
                fnames: edited,
                context: context,
                aider_edits: true,
                coder: this
            });

            if (res) {
                const [commit_hash, commit_message] = res;
                this.show_auto_commit_outcome(res);
                return this.gpt_prompts.files_content_gpt_edits
                    .replace('{hash}', commit_hash)
                    .replace('{message}', commit_message);
            }

            return this.gpt_prompts.files_content_gpt_no_edits;
        } catch (error) {
            this.io.tool_error(`Unable to commit: ${error}`);
            return null;
        }
    }

    show_auto_commit_outcome(res: [string, string]): void {
        const [commit_hash, commit_message] = res;
        this.last_aider_commit_hash = commit_hash;
        this.aider_commit_hashes.add(commit_hash);
        if (this.show_diffs) {
            this.commands.cmd_diff();
        }
    }

    get_context_from_history(history: ChatMessage[]): string {
        let context = "";
        for (const msg of history) {
            context += `\n${msg.role.toUpperCase()}: ${msg.content}\n`;
        }
        return context;
    }

    // ========================================================================
    // Utility Methods
    // ========================================================================

    async reply_completed(): Promise<void> {
        // Default implementation - subclasses can override
    }

    copy_context(): void {
        if (this.auto_copy_context) {
            this.commands.cmd_copy_context();
        }
    }

    async get_input(): Promise<string> {
        const inchat_files = this.get_inchat_relative_files();
        const read_only_files = Array.from(this.abs_read_only_fnames).map(fname => this.get_rel_fname(fname));
        const all_files = [...new Set([...inchat_files, ...read_only_files])].sort();
        const edit_format = this.edit_format === this.main_model.edit_format ? "" : this.edit_format || "";

        return this.io.get_input(
            this.root,
            all_files,
            this.get_addable_relative_files(),
            this.commands,
            this.abs_read_only_fnames,
            edit_format
        );
    }

    check_for_file_mentions(content: string): string | null {
        // Placeholder implementation
        return null;
    }

    check_for_urls(inp: string): string {
        // Placeholder implementation - just return input unchanged
        return inp;
    }

    keyboard_interrupt(): void {
        const now = Date.now();
        const thresh = 2000; // 2 seconds

        if (this.last_keyboard_interrupt && now - this.last_keyboard_interrupt.getTime() < thresh) {
            this.io.tool_warning("\n\n^C KeyboardInterrupt");
            this.emit('exit', { reason: 'Control-C' });
            process.exit(0);
        }

        this.io.tool_warning("\n\n^C again to exit");
        this.last_keyboard_interrupt = new Date(now);
    }

    move_back_cur_messages(message: string | null): void {
        this.done_messages.push(...this.cur_messages);
        this.summarize_start();

        if (message) {
            this.done_messages.push(
                { role: "user", content: message },
                { role: "assistant", content: "Ok." }
            );
        }
        this.cur_messages = [];
    }

    summarize_start(): void {
        // Placeholder - would implement chat history summarization
    }

    summarize_end(): void {
        // Placeholder - would implement chat history summarization
    }

    show_undo_hint(): void {
        if (this.commit_before_message.length === 0 || !this.repo) {
            return;
        }
        if (this.commit_before_message[this.commit_before_message.length - 1] !== this.repo.get_head_commit_sha()) {
            this.io.tool_output("You can use /undo to undo and discard each aider commit.");
        }
    }

    lint_edited(edited: Set<string>): string {
        let res = "";
        for (const fname of edited) {
            if (!fname) continue;
            const errors = this.linter.lint(this.abs_root_path(fname));
            if (errors) {
                res += `\n${errors}\n`;
            }
        }

        if (res) {
            this.io.tool_warning(res);
        }

        return res;
    }

    run_shell_commands(): string {
        if (!this.suggest_shell_commands) {
            return "";
        }

        let accumulated_output = "";
        const done = new Set<string>();

        for (const command of this.shell_commands) {
            if (done.has(command)) {
                continue;
            }
            done.add(command);

            if (this.io.confirm_ask("Run shell command?", command, undefined, false, true)) {
                this.io.tool_output(`Running ${command}`);
                // This would execute the shell command in a real implementation
                // For now, just acknowledge it
                accumulated_output += `Executed: ${command}\n`;
            }
        }

        return accumulated_output;
    }

    // ========================================================================
    // Factory Helper Methods
    // ========================================================================

    private create_commands(): Commands {
        // This would return a proper Commands instance
        return {
            io: this.io,
            coder: this,
            clone: () => this.create_commands(),
            is_command: (input: string) => input.startsWith('/'),
            run: (input: string) => null,
            cmd_copy_context: () => {},
            cmd_test: (test_cmd?: string) => null,
            cmd_diff: () => {},
            cmd_web: (url: string, return_content?: boolean) => ""
        };
    }

    private create_summarizer(): ChatSummary {
        // This would return a proper ChatSummary instance
        return {
            too_big: (messages: ChatMessage[]) => false,
            summarize: (messages: ChatMessage[]) => messages,
            summarize_all: (messages: ChatMessage[]) => messages
        };
    }

    private create_linter(): Linter {
        // This would return a proper Linter instance
        return {
            lint: (path: string) => "",
            set_linter: (lang: string, cmd: string) => {}
        };
    }
