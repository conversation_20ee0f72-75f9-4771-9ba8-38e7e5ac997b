import { BaseCoder } from "./BaseCoder";
import { ArchitectCoder } from "./architect_coder";
import { AskCoder } from "./ask_coder";
import { ContextCoder } from "./context_coder";
import { EditBlockCoder } from "./editblock_coder";
import { EditBlockFencedCoder } from "./editblock_fenced_coder";
import { EditorDiffFencedCoder } from "./editor_diff_fenced_coder";
import { EditorEditBlockCoder } from "./editor_editblock_coder";
import { EditorWholeFileCoder } from "./editor_whole_coder";
import { HelpCoder } from "./help_coder";
import { PatchCoder } from "./patch_coder";
import { UnifiedDiffCoder } from "./udiff_coder";
import { UnifiedDiffSimpleCoder } from "./udiff_simple";
import { WholeFileCoder } from "./wholefile_coder";
import { SingleWholeFileFunctionCoder } from "./single_wholefile_func_coder";
import { WholeFileFunctionCoder } from "./wholefile_func_coder";

// Type for coder constructor
export type CoderConstructor = {
  new (main_model: any, io: any, options?: any): BaseCoder;
  edit_format?: string;
};

// Registry of all available coders
class CoderRegistry {
  private coders: Map<string, CoderConstructor> = new Map();

  constructor() {
    this.registerDefaultCoders();
  }

  private registerDefaultCoders(): void {
    // Register all built-in coders
    this.register(HelpCoder);
    this.register(AskCoder);
    this.register(EditBlockCoder);
    this.register(EditBlockFencedCoder);
    this.register(WholeFileCoder);
    this.register(PatchCoder);
    this.register(UnifiedDiffCoder);
    this.register(UnifiedDiffSimpleCoder);
    this.register(ArchitectCoder);
    this.register(EditorEditBlockCoder);
    this.register(EditorWholeFileCoder);
    this.register(EditorDiffFencedCoder);
    this.register(ContextCoder);
    this.register(SingleWholeFileFunctionCoder);
    this.register(WholeFileFunctionCoder);
  }

  register(CoderClass: CoderConstructor): void {
    const editFormat = (CoderClass as any).edit_format;
    if (editFormat) {
      this.coders.set(editFormat, CoderClass);
    }
  }

  get(editFormat: string): CoderConstructor | undefined {
    return this.coders.get(editFormat);
  }

  getAll(): CoderConstructor[] {
    return Array.from(this.coders.values());
  }

  getValidFormats(): string[] {
    return Array.from(this.coders.keys()).filter(format => format !== null);
  }

  has(editFormat: string): boolean {
    return this.coders.has(editFormat);
  }

  create(options: {
    main_model?: any;
    edit_format?: string;
    io?: any;
    from_coder?: BaseCoder;
    summarize_from_coder?: boolean;
    [key: string]: any;
  } = {}): BaseCoder {
    const main_model = options.main_model || options.from_coder?.main_model;
    if (!main_model) {
      throw new Error("main_model is required");
    }

    const io = options.io || options.from_coder?.io;
    if (!io) {
      throw new Error("io is required");
    }

    let edit_format = options.edit_format;
    if (edit_format === "code") {
      edit_format = undefined;
    }
    if (!edit_format) {
      edit_format = options.from_coder?.edit_format || main_model.edit_format;
    }

    // Handle inheritance from existing coder
    if (options.from_coder) {
      const use_kwargs = { ...options.from_coder.original_kwargs };

      // If the edit format changes, we may need to summarize chat history
      let done_messages = options.from_coder.done_messages;
      if (edit_format !== options.from_coder.edit_format &&
          done_messages.length > 0 &&
          options.summarize_from_coder !== false) {
        try {
          if (options.from_coder.summarizer) {
            done_messages = options.from_coder.summarizer.summarize_all(done_messages);
          }
        } catch (error) {
          io.tool_warning("Chat history summarization failed, continuing with full history");
        }
      }

      // Bring along context from the old Coder
      const update = {
        fnames: Array.from(options.from_coder.abs_fnames || []),
        read_only_fnames: Array.from(options.from_coder.abs_read_only_fnames || []),
        done_messages: done_messages,
        cur_messages: options.from_coder.cur_messages,
        aider_commit_hashes: options.from_coder.aider_commit_hashes,
        commands: options.from_coder.commands?.clone(),
        total_cost: options.from_coder.total_cost,
        ignore_mentions: options.from_coder.ignore_mentions,
        total_tokens_sent: options.from_coder.total_tokens_sent,
        total_tokens_received: options.from_coder.total_tokens_received,
        file_watcher: options.from_coder.file_watcher,
      };

      Object.assign(use_kwargs, update);
      Object.assign(use_kwargs, options);

      options = use_kwargs;
      if (options.from_coder) {
        options.from_coder.ok_to_warm_cache = false;
      }
    }

    const CoderClass = this.get(edit_format);
    if (CoderClass) {
      const coder = new CoderClass(main_model, io, options);
      (coder as any).original_kwargs = { ...options };
      return coder;
    }

    const valid_formats = this.getValidFormats();
    throw new Error(
      `Unknown edit format ${edit_format}. Valid formats are: ${valid_formats.join(', ')}`
    );
  }
}

// Create global registry instance
export const coderRegistry = new CoderRegistry();

// Update BaseCoder's static method to use the registry
(BaseCoder as any).get_all_coders = () => coderRegistry.getAll();
(BaseCoder as any).create = (options: any = {}) => coderRegistry.create(options);

// Export for external use
export { CoderRegistry };

// Legacy exports for compatibility
export const Coder = {
  create: (options: any = {}) => coderRegistry.create(options),
  get_all_coders: () => coderRegistry.getAll(),
};

// Export all coder classes for direct use
export {
  BaseCoder,
  ArchitectCoder,
  AskCoder,
  ContextCoder,
  EditBlockCoder,
  EditBlockFencedCoder,
  EditorDiffFencedCoder,
  EditorEditBlockCoder,
  EditorWholeFileCoder,
  HelpCoder,
  PatchCoder,
  UnifiedDiffCoder,
  UnifiedDiffSimpleCoder,
  WholeFileCoder,
  SingleWholeFileFunctionCoder,
  WholeFileFunctionCoder,
};
