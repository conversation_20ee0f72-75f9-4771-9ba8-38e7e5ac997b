# aider-ts

AI pair programming in TypeScript - Chat with your codebase, edit files, get things done.

## Overview

aider-ts is a TypeScript/Node.js port of the popular [aider](https://aider.chat) AI coding assistant. It lets you pair program with LLMs like GPT-4, <PERSON>, and others right in your terminal. You can ask for changes to your code, and aider will edit your files for you.

## Features

- 🤖 **Multiple LLM Support**: Works with OpenAI GPT-4, Anthropic Claude, and many others
- 📝 **Direct File Editing**: AI directly edits your source files
- 🔄 **Git Integration**: Automatic git commits with meaningful messages
- 🎯 **Smart Context**: Understands your codebase structure
- 🛠️ **Multiple Edit Formats**: Choose how the AI makes changes
- 🔍 **Code Understanding**: Uses tree-sitter for syntax awareness
- 🎨 **Rich Terminal UI**: Beautiful, interactive command-line interface
- ⚡ **Fast**: Optimized for quick iteration

## Installation

### Prerequisites

- Node.js 16+ 
- Git
- An API key for your preferred LLM provider

### Install from npm

```bash
npm install -g aider-ts
```

### Install from source

```bash
git clone https://github.com/Aider-AI/aider-ts.git
cd aider-ts
npm install
npm run build
npm link
```

## Quick Start

1. **Set up your API key**:
   ```bash
   export OPENAI_API_KEY=your-key-here
   # or
   export ANTHROPIC_API_KEY=your-key-here
   ```

2. **Start aider in your project**:
   ```bash
   cd your-project
   aider-ts
   ```

3. **Add files to the chat**:
   ```
   > /add src/main.ts src/utils.ts
   ```

4. **Ask for changes**:
   ```
   > Add error handling to the main function
   ```

## Usage Examples

### Basic Usage

```bash
# Start with specific files
aider-ts src/app.ts src/types.ts

# Use a specific model
aider-ts --model claude-3-sonnet src/app.ts

# Work with the whole project
aider-ts --files $(find src -name "*.ts")
```

### Chat Commands

Once in the chat, you can use these commands:

- `/add <files>` - Add files to the chat
- `/drop <files>` - Remove files from the chat  
- `/clear` - Clear chat history
- `/commit` - Commit current changes
- `/diff` - Show pending changes
- `/help` - Show all commands
- `/quit` - Exit aider
- `/run <cmd>` - Run a shell command
- `/test` - Run tests
- `/undo` - Undo the last change

### Configuration

Create a `.aider.conf.yml` file in your project or home directory:

```yaml
# Model configuration
model: gpt-4
weak-model: gpt-3.5-turbo

# Edit format
edit-format: diff

# Git settings
auto-commits: true
dirty-commits: true

# File settings
auto-lint: true
auto-test: false

# UI settings
pretty: true
stream: true
```

Or use environment variables:

```bash
export AIDER_MODEL=claude-3-sonnet
export AIDER_AUTO_COMMITS=true
export AIDER_DARK_MODE=true
```

## Supported Models

### OpenAI
- GPT-4 Turbo
- GPT-4
- GPT-3.5 Turbo

### Anthropic  
- Claude 3 Opus
- Claude 3 Sonnet
- Claude 3 Haiku

### Others
- DeepSeek Coder
- Groq models
- Local models via Ollama
- Any OpenAI-compatible API

## Edit Formats

Choose how aider makes changes to your code:

- **`diff`** - Uses search/replace blocks (recommended)
- **`whole`** - Sends back whole updated files  
- **`editor-diff`** - Minimal diffs for simple changes
- **`architect`** - High-level planning mode

## Development

### Setup

```bash
git clone https://github.com/Aider-AI/aider-ts.git
cd aider-ts
npm install
```

### Build

```bash
npm run build
```

### Development Mode

```bash
npm run dev
```

### Testing

```bash
npm test
```

### Linting

```bash
npm run lint
npm run format
```

## Project Structure

```
aider-ts/
├── src/
│   ├── analytics/      # Usage analytics
│   ├── args/           # Command-line argument parsing
│   ├── coders/         # Different AI coding strategies
│   ├── commands/       # Chat commands
│   ├── io/             # Input/output handling
│   ├── models/         # LLM integration
│   ├── prompts/        # AI prompts and templates
│   ├── repo/           # Git repository handling
│   ├── utils/          # Utility functions
│   ├── main.ts         # Main application entry
│   └── index.ts        # Package exports
├── bin/                # Executable scripts
├── dist/               # Compiled JavaScript
└── docs/               # Documentation
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | - |
| `ANTHROPIC_API_KEY` | Anthropic API key | - |
| `AIDER_MODEL` | Default model to use | `gpt-4` |
| `AIDER_AUTO_COMMITS` | Auto-commit changes | `true` |
| `AIDER_DARK_MODE` | Use dark mode | `false` |
| `AIDER_NO_AUTO_TEST` | Disable auto-testing | `false` |

## Troubleshooting

### Common Issues

**API Key not found**
```bash
export OPENAI_API_KEY=your-key-here
# or add to ~/.bashrc or ~/.zshrc
```

**File encoding issues**
```bash
# Use UTF-8 encoding
export LANG=en_US.UTF-8
```

**Git not initialized**
```bash
git init
git add .
git commit -m "Initial commit"
```

### Debug Mode

Run with verbose output:
```bash
aider-ts --verbose
```

## Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Development Workflow

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## License

Apache 2.0 License - see [LICENSE](LICENSE) for details.

## Links

- [Documentation](https://aider.chat/docs/)
- [GitHub Issues](https://github.com/Aider-AI/aider-ts/issues)
- [Discord Community](https://discord.gg/aider)
- [Original Python Version](https://github.com/paul-gauthier/aider)

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for release notes and version history.

---

**Note**: This is a TypeScript port of the original Python aider. While we strive for feature parity, some features may not be available yet. Please check the documentation or open an issue if you need specific functionality.