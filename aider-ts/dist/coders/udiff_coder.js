"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedDiffCoder = void 0;
const BaseCoder_1 = require("./BaseCoder");
const udiff_prompts_1 = require("./udiff_prompts");
const diff_1 = require("diff");
// TODO: Implement a proper diffing/patching utility
const noMatchError = `UnifiedDiffNoMatch: hunk failed to apply!\n\n{path} does not contain lines that match the diff you provided!\nTry again.\nDO NOT skip blank lines, comments, docstrings, etc!\nThe diff needs to apply cleanly to the lines in {path}!\n\n{path} does not contain these {num_lines} exact lines in a row:\n\`\`\`\n{original}\`\`\`\n`;
const notUniqueError = `UnifiedDiffNotUnique: hunk failed to apply!\n\n{path} contains multiple sets of lines that match the diff you provided!\nTry again.\nUse additional \` \` lines to provide context that uniquely indicates which code needs to be changed.\nThe diff needs to apply to a unique set of lines in {path}!\n\n{path} contains multiple copies of these {num_lines} lines:\n\`\`\`\n{original}\`\`\`\n`;
const otherHunksApplied = "Note: some hunks did apply successfully. See the updated source code shown above.\n\n";
function find_diffs(content) {
    if (!content.endsWith('\n')) {
        content += '\n';
    }
    const lines = content.split(/\r?\n/).map(l => l + '\n');
    let lineNum = 0;
    const edits = [];
    while (lineNum < lines.length) {
        const line = lines[lineNum];
        if (line.startsWith('```diff')) {
            const [newLineNum, theseEdits] = processFencedBlock(lines, lineNum + 1);
            lineNum = newLineNum;
            edits.push(...theseEdits);
        }
        else {
            lineNum++;
        }
    }
    return edits;
}
function processFencedBlock(lines, startLineNum) {
    let lineNum = startLineNum;
    while (lineNum < lines.length && !lines[lineNum].startsWith('```')) {
        lineNum++;
    }
    const block = lines.slice(startLineNum, lineNum);
    block.push('@@ @@\n');
    let fname = null;
    if (block[0].startsWith('--- ') && block[1].startsWith('+++ ')) {
        const aFname = block[0].substring(4).trim();
        const bFname = block[1].substring(4).trim();
        if ((aFname.startsWith('a/') || aFname === '/dev/null') && bFname.startsWith('b/')) {
            fname = bFname.substring(2);
        }
        else {
            fname = bFname;
        }
        block.splice(0, 2);
    }
    const edits = [];
    let hunk = [];
    let keeper = false;
    for (const line of block) {
        hunk.push(line);
        if (line.length < 2)
            continue;
        if (line.startsWith('+++ ') && hunk[hunk.length - 2].startsWith('--- ')) {
            edits.push([fname, hunk.slice(0, -2)]);
            hunk = [];
            keeper = false;
            fname = line.substring(4).trim();
            continue;
        }
        const op = line[0];
        if (op === '-' || op === '+') {
            keeper = true;
            continue;
        }
        if (op !== '@')
            continue;
        if (!keeper) {
            hunk = [];
            continue;
        }
        edits.push([fname, hunk.slice(0, -1)]);
        hunk = [];
        keeper = false;
    }
    return [lineNum + 1, edits];
}
function hunkToBeforeAfter(hunk) {
    const before = hunk.filter(line => line.startsWith('-') || line.startsWith(' ')).map(line => line.substring(1)).join('');
    const after = hunk.filter(line => line.startsWith('+') || line.startsWith(' ')).map(line => line.substring(1)).join('');
    return [before, after];
}
class UnifiedDiffCoder extends BaseCoder_1.BaseCoder {
    constructor() {
        super(...arguments);
        this.gptPrompts = new udiff_prompts_1.UnifiedDiffPrompts();
    }
    getEdits() {
        const content = this.partialResponseContent;
        const rawEdits = find_diffs(content);
        let lastPath = null;
        const edits = [];
        for (const [path, hunk] of rawEdits) {
            if (path) {
                lastPath = path;
                edits.push([path, hunk]);
            }
            else if (lastPath) {
                edits.push([lastPath, hunk]);
            }
        }
        return edits;
    }
    applyEdits(edits) {
        let patchString = '';
        for (const [path, hunk] of edits) {
            patchString += `--- a/${path}\n`;
            patchString += `+++ b/${path}\n`;
            patchString += hunk.join('');
        }
        (0, diff_1.applyPatches)(patchString, {
            loadFile: (patch, callback) => {
                if (!patch.oldFileName) {
                    callback("Patch is missing a filename.", '');
                    return;
                }
                const content = this.io.read_text(patch.oldFileName.substring(2));
                if (content === null) {
                    // Create a new file if it doesn't exist and the patch indicates it
                    if (patch.newHeader) {
                        callback(undefined, "");
                    }
                    else {
                        callback(`File not found: ${patch.oldFileName}`, '');
                    }
                }
                else {
                    callback(undefined, content);
                }
            },
            patched: (patch, patchedContent, callback) => {
                if (patchedContent === false) {
                    callback(`Failed to apply patch to ${patch.oldFileName}`);
                    return;
                }
                if (!patch.oldFileName) {
                    callback("Patch is missing a filename for writing.");
                    return;
                }
                if (typeof patchedContent === 'string') {
                    this.io.write_text(patch.oldFileName.substring(2), patchedContent);
                }
                callback(undefined);
            },
            complete: (err) => {
                if (err) {
                    // TODO: Handle errors more gracefully, possibly by raising an exception
                    // that can be caught and displayed to the user, similar to the python version.
                    console.error("Failed to apply patch:", err);
                }
            }
        });
    }
}
exports.UnifiedDiffCoder = UnifiedDiffCoder;
UnifiedDiffCoder.editFormat = "udiff";
