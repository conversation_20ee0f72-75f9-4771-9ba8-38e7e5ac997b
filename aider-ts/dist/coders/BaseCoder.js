"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCoder = exports.FinishReasonLength = exports.MissingAPIKeyError = exports.UnknownEditFormat = exports.ChatChunks = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const events_1 = require("events");
const __version__ = "0.0.1-ts";
// ============================================================================
// Chat Chunks Helper Class
// ============================================================================
class ChatChunks {
    constructor() {
        this.system = [];
        this.examples = [];
        this.done = [];
        this.repo = [];
        this.readonly_files = [];
        this.chat_files = [];
        this.cur = [];
        this.reminder = [];
    }
    all_messages() {
        return [
            ...this.system,
            ...this.examples,
            ...this.readonly_files,
            ...this.repo,
            ...this.done,
            ...this.chat_files,
            ...this.cur,
            ...this.reminder
        ];
    }
    add_cache_control_headers() {
        if (this.examples.length > 0) {
            this.add_cache_control(this.examples);
        }
        else {
            this.add_cache_control(this.system);
        }
        if (this.repo.length > 0) {
            this.add_cache_control(this.repo);
        }
        else {
            this.add_cache_control(this.readonly_files);
        }
        this.add_cache_control(this.chat_files);
    }
    add_cache_control(messages) {
        if (!messages || messages.length === 0)
            return;
        const lastMessage = messages[messages.length - 1];
        let content = lastMessage.content;
        if (typeof content === 'string') {
            content = [{
                    type: "text",
                    text: content,
                    cache_control: { type: "ephemeral" }
                }];
        }
        else if (Array.isArray(content) && content.length > 0) {
            content[0].cache_control = { type: "ephemeral" };
        }
        lastMessage.content = content;
    }
    cacheable_messages() {
        const messages = this.all_messages();
        for (let i = messages.length - 1; i >= 0; i--) {
            const message = messages[i];
            if (Array.isArray(message.content) &&
                message.content[0] &&
                message.content[0].cache_control) {
                return messages.slice(0, i + 1);
            }
        }
        return messages;
    }
}
exports.ChatChunks = ChatChunks;
// ============================================================================
// Exceptions
// ============================================================================
class UnknownEditFormat extends Error {
    constructor(edit_format, valid_formats) {
        super(`Unknown edit format ${edit_format}. Valid formats are: ${valid_formats.join(', ')}`);
        this.edit_format = edit_format;
        this.valid_formats = valid_formats;
    }
}
exports.UnknownEditFormat = UnknownEditFormat;
class MissingAPIKeyError extends Error {
}
exports.MissingAPIKeyError = MissingAPIKeyError;
class FinishReasonLength extends Error {
}
exports.FinishReasonLength = FinishReasonLength;
// ============================================================================
// Main BaseCoder Class
// ============================================================================
class BaseCoder extends events_1.EventEmitter {
    constructor(main_model, io, options = {}) {
        super();
        // Core properties
        this.abs_fnames = new Set();
        this.abs_read_only_fnames = new Set();
        this.repo = null;
        this.last_aider_commit_hash = null;
        this.aider_edited_files = null;
        this.last_asked_for_commit_time = 0;
        this.repo_map = null;
        this.functions = null;
        this.num_exhausted_context_windows = 0;
        this.num_malformed_responses = 0;
        this.last_keyboard_interrupt = null;
        this.num_reflections = 0;
        this.max_reflections = 3;
        this.edit_format = null;
        this.yield_stream = false;
        this.temperature = null;
        this.auto_lint = true;
        this.auto_test = false;
        this.test_cmd = null;
        this.lint_outcome = null;
        this.test_outcome = null;
        this.multi_response_content = "";
        this.partial_response_content = "";
        this.partial_response_function_call = {};
        this.commit_before_message = [];
        this.message_cost = 0.0;
        this.add_cache_headers = false;
        this.cache_warming_thread = null;
        this.num_cache_warming_pings = 0;
        this.suggest_shell_commands = true;
        this.detect_urls = true;
        this.ignore_mentions = new Set();
        this.chat_language = null;
        this.commit_language = null;
        this.file_watcher = null;
        this.verbose = false;
        this.show_diffs = false;
        this.auto_commits = true;
        this.dirty_commits = true;
        this.dry_run = false;
        this.stream = true;
        this.cur_messages = [];
        this.done_messages = [];
        this.aider_commit_hashes = new Set();
        this.summarizer_thread = null;
        this.summarized_done_messages = [];
        this.summarizing_messages = null;
        this.total_cost = 0.0;
        this.total_tokens_sent = 0;
        this.total_tokens_received = 0;
        this.message_tokens_sent = 0;
        this.message_tokens_received = 0;
        this.usage_report = null;
        // Internal properties
        this.rejected_urls = new Set();
        this.abs_root_path_cache = {};
        this.chat_completion_call_hashes = [];
        this.chat_completion_response_hashes = [];
        this.need_commit_before_edits = new Set();
        this.auto_copy_context = false;
        this.auto_accept_architect = true;
        this.original_kwargs = {};
        this.ok_to_warm_cache = false;
        this.reflected_message = null;
        this.shell_commands = [];
        this.waiting_spinner = null;
        this.mdstream = null;
        // Fence handling
        this.fences = [
            ['```', '```'],
            ['````', '````'],
            ['<source>', '</source>'],
            ['<code>', '</code>'],
            ['<pre>', '</pre>'],
            ['<codeblock>', '</codeblock>'],
            ['<sourcecode>', '</sourcecode>']
        ];
        this.fence = this.fences[0];
        this.main_model = main_model;
        this.io = io;
        this.repo = options.repo || null;
        this.show_diffs = options.show_diffs || false;
        this.auto_commits = options.auto_commits !== undefined ? options.auto_commits : true;
        this.dirty_commits = options.dirty_commits !== undefined ? options.dirty_commits : true;
        this.dry_run = options.dry_run || false;
        this.verbose = options.verbose || false;
        this.stream = options.stream !== undefined ? options.stream : true;
        this.cur_messages = options.cur_messages || [];
        this.done_messages = options.done_messages || [];
        this.auto_lint = options.auto_lint !== undefined ? options.auto_lint : true;
        this.auto_test = options.auto_test || false;
        this.test_cmd = options.test_cmd || null;
        this.aider_commit_hashes = options.aider_commit_hashes || new Set();
        this.total_cost = options.total_cost || 0.0;
        this.suggest_shell_commands = options.suggest_shell_commands !== undefined ? options.suggest_shell_commands : true;
        this.chat_language = options.chat_language || null;
        this.commit_language = options.commit_language || null;
        this.detect_urls = options.detect_urls !== undefined ? options.detect_urls : true;
        this.ignore_mentions = options.ignore_mentions || new Set();
        this.total_tokens_sent = options.total_tokens_sent || 0;
        this.total_tokens_received = options.total_tokens_received || 0;
        this.file_watcher = options.file_watcher || null;
        this.auto_copy_context = options.auto_copy_context || false;
        this.auto_accept_architect = options.auto_accept_architect !== undefined ? options.auto_accept_architect : true;
        this.num_cache_warming_pings = options.num_cache_warming_pings || 0;
        if (options.cache_prompts && this.main_model.cache_control) {
            this.add_cache_headers = true;
        }
        // Set up root directory
        if (this.repo) {
            this.root = this.repo.root;
        }
        else {
            this.root = process.cwd();
        }
        // Initialize file watcher
        if (this.file_watcher) {
            this.file_watcher.coder = this;
        }
        // Process initial file names
        const fnames = options.fnames || [];
        for (const fname of fnames) {
            this.add_file(fname);
        }
        // Process read-only files
        const read_only_fnames = options.read_only_fnames || [];
        for (const fname of read_only_fnames) {
            const abs_fname = this.abs_root_path(fname);
            if (fs_1.default.existsSync(abs_fname)) {
                this.abs_read_only_fnames.add(abs_fname);
            }
            else {
                this.io.tool_warning(`Error: Read-only file ${fname} does not exist. Skipping.`);
            }
        }
        // Initialize dependencies
        this.commands = options.commands || this.create_commands();
        this.summarizer = options.summarizer || this.create_summarizer();
        this.linter = this.create_linter();
        // Set up streaming
        this.stream = this.stream && this.main_model.streaming;
        this.original_kwargs = { ...options };
    }
    // ========================================================================
    // Factory Methods
    // ========================================================================
    static create(options = {}) {
        const main_model = options.main_model || (options.from_coder?.main_model);
        if (!main_model) {
            throw new Error("main_model is required");
        }
        const io = options.io || options.from_coder?.io;
        if (!io) {
            throw new Error("io is required");
        }
        let edit_format = options.edit_format;
        if (edit_format === "code") {
            edit_format = null;
        }
        if (!edit_format) {
            edit_format = options.from_coder?.edit_format || main_model.edit_format;
        }
        // Import all coder classes dynamically
        const coders = BaseCoder.get_all_coders();
        for (const CoderClass of coders) {
            if ('edit_format' in CoderClass && CoderClass.edit_format === edit_format) {
                return new CoderClass(main_model, io, options);
            }
        }
        const valid_formats = coders
            .filter(c => 'edit_format' in c && c.edit_format !== null)
            .map(c => c.edit_format);
        throw new UnknownEditFormat(edit_format, valid_formats);
    }
    // This should be overridden by the registry system
    static get_all_coders() {
        // This is a placeholder - in a real implementation, this would
        // return all registered coder classes
        return [];
    }
    clone(options = {}) {
        const new_options = { ...this.original_kwargs, ...options, from_coder: this };
        return BaseCoder.create(new_options);
    }
    // ========================================================================
    // Core Methods
    // ========================================================================
    add_file(fname) {
        const file_path = path_1.default.resolve(fname);
        // Check if file exists, create if needed
        if (!fs_1.default.existsSync(file_path)) {
            try {
                fs_1.default.writeFileSync(file_path, '', 'utf8');
                this.io.tool_output(`Creating empty file ${fname}`);
            }
            catch (error) {
                this.io.tool_warning(`Can not create ${fname}, skipping.`);
                return;
            }
        }
        // Check if it's a regular file
        const stats = fs_1.default.statSync(file_path);
        if (!stats.isFile()) {
            this.io.tool_warning(`Skipping ${fname} that is not a normal file.`);
            return;
        }
        this.abs_fnames.add(file_path);
        this.check_added_files();
    }
    check_added_files() {
        const warn_number_of_files = 4;
        const warn_number_of_tokens = 20 * 1024;
        const num_files = this.abs_fnames.size;
        if (num_files < warn_number_of_files) {
            return;
        }
        let tokens = 0;
        for (const fname of this.abs_fnames) {
            if (this.is_image_file(fname)) {
                continue;
            }
            const content = this.io.read_text(fname);
            if (content && this.main_model.token_count) {
                tokens += this.main_model.token_count(content);
            }
        }
        if (tokens < warn_number_of_tokens) {
            return;
        }
        this.io.tool_warning("Warning: it's best to only add files that need changes to the chat.");
    }
    abs_root_path(file_path) {
        if (this.abs_root_path_cache[file_path]) {
            return this.abs_root_path_cache[file_path];
        }
        const resolved = path_1.default.resolve(this.root, file_path);
        this.abs_root_path_cache[file_path] = resolved;
        return resolved;
    }
    get_rel_fname(fname) {
        return path_1.default.relative(this.root, fname);
    }
    get_inchat_relative_files() {
        return Array.from(this.abs_fnames).map(fname => this.get_rel_fname(fname)).sort();
    }
    get_all_relative_files() {
        if (this.repo) {
            return this.repo.get_tracked_files().sort();
        }
        else {
            return this.get_inchat_relative_files();
        }
    }
    get_addable_relative_files() {
        const all_files = new Set(this.get_all_relative_files());
        const inchat_files = new Set(this.get_inchat_relative_files());
        const read_only_files = new Set(Array.from(this.abs_read_only_fnames).map(f => this.get_rel_fname(f)));
        return Array.from(all_files).filter(f => !inchat_files.has(f) && !read_only_files.has(f));
    }
    // ========================================================================
    // Message and Chat Management
    // ========================================================================
    async run(options = {}) {
        try {
            if (options.with_message) {
                this.io.user_input(options.with_message);
                await this.run_one(options.with_message, options.preproc !== false);
                return this.partial_response_content;
            }
            while (true) {
                try {
                    if (!this.io.placeholder) {
                        this.copy_context();
                    }
                    const user_message = await this.get_input();
                    await this.run_one(user_message, options.preproc !== false);
                    this.show_undo_hint();
                }
                catch (error) {
                    if (error instanceof Error && error.message === 'KeyboardInterrupt') {
                        this.keyboard_interrupt();
                    }
                    else {
                        throw error;
                    }
                }
            }
        }
        catch (error) {
            if (error instanceof Error && error.message === 'EOFError') {
                return;
            }
            throw error;
        }
    }
    async run_one(user_message, preproc) {
        this.init_before_message();
        let message = user_message;
        if (preproc) {
            const processed = this.preproc_user_input(user_message);
            if (processed !== undefined) {
                message = processed;
            }
        }
        while (message) {
            this.reflected_message = null;
            await this.send_message(message);
            if (!this.reflected_message) {
                break;
            }
            if (this.num_reflections >= this.max_reflections) {
                this.io.tool_warning(`Only ${this.max_reflections} reflections allowed, stopping.`);
                return;
            }
            this.num_reflections += 1;
            message = this.reflected_message;
        }
    }
    init_before_message() {
        this.aider_edited_files = new Set();
        this.reflected_message = null;
        this.num_reflections = 0;
        this.lint_outcome = null;
        this.test_outcome = null;
        this.shell_commands = [];
        this.message_cost = 0;
        if (this.repo) {
            this.commit_before_message.push(this.repo.get_head_commit_sha());
        }
    }
    preproc_user_input(inp) {
        if (!inp) {
            return;
        }
        if (this.commands.is_command(inp)) {
            return this.commands.run(inp) || undefined;
        }
        this.check_for_file_mentions(inp);
        inp = this.check_for_urls(inp);
        return inp;
    }
    async send_message(inp) {
        this.emit('message_send_starting');
        this.io.llm_started();
        this.cur_messages.push({
            role: "user",
            content: inp,
        });
        const chunks = this.format_messages();
        const messages = chunks.all_messages();
        if (!this.check_tokens(messages)) {
            return;
        }
        if (this.verbose) {
            console.log("Messages:", JSON.stringify(messages, null, 2));
        }
        this.multi_response_content = "";
        if (this.show_pretty()) {
            // this.waiting_spinner = new WaitingSpinner("Waiting for " + this.main_model.name);
            // this.waiting_spinner.start();
            if (this.stream) {
                this.mdstream = this.io.get_assistant_mdstream?.();
            }
        }
        try {
            if (this.main_model.send_completion) {
                const [hash_object, completion] = await this.main_model.send_completion(messages, this.functions, this.stream, this.temperature);
                this.chat_completion_call_hashes.push(hash_object);
                if (this.stream) {
                    await this.show_send_output_stream(completion);
                }
                else {
                    this.show_send_output(completion);
                }
                this.calculate_and_show_tokens_and_cost(messages, completion);
            }
        }
        catch (error) {
            this.io.tool_error(`Error sending message: ${error}`);
            return;
        }
        finally {
            this.stop_waiting_spinner();
            this.partial_response_content = this.get_multi_response_content_in_progress(true);
            this.multi_response_content = "";
            if (this.mdstream) {
                this.live_incremental_response(true);
                this.mdstream = null;
            }
        }
        this.io.tool_output("");
        this.show_usage_report();
        this.add_assistant_reply_to_cur_messages();
        // Handle reply completion
        try {
            await this.reply_completed();
        }
        catch (error) {
            if (error instanceof Error && error.message === 'KeyboardInterrupt') {
                this.keyboard_interrupt();
                return;
            }
            throw error;
        }
        // Apply edits
        const edited = this.apply_updates();
        if (edited.size > 0) {
            this.aider_edited_files = new Set([...this.aider_edited_files || [], ...edited]);
            const saved_message = this.auto_commit(edited);
            this.move_back_cur_messages(saved_message);
        }
        // Auto-lint if needed
        if (edited.size > 0 && this.auto_lint) {
            const lint_errors = this.lint_edited(edited);
            if (lint_errors) {
                this.lint_outcome = false;
                const ok = this.io.confirm_ask("Attempt to fix lint errors?");
                if (ok) {
                    this.reflected_message = lint_errors;
                    return;
                }
            }
            else {
                this.lint_outcome = true;
            }
        }
        // Run shell commands
        const shared_output = this.run_shell_commands();
        if (shared_output) {
            this.cur_messages.push({ role: "user", content: shared_output }, { role: "assistant", content: "Ok" });
        }
        // Auto-test if needed
        if (edited.size > 0 && this.auto_test) {
            const test_errors = this.commands.cmd_test(this.test_cmd);
            if (test_errors) {
                this.test_outcome = false;
                const ok = this.io.confirm_ask("Attempt to fix test errors?");
                if (ok) {
                    this.reflected_message = test_errors;
                    return;
                }
            }
            else {
                this.test_outcome = true;
            }
        }
    }
    // ========================================================================
    // Message Formatting
    // ========================================================================
    format_messages() {
        this.choose_fence();
        const chunks = new ChatChunks();
        // System message
        const main_sys = this.fmt_system_prompt(this.gpt_prompts.main_system);
        if (this.main_model.use_system_prompt) {
            chunks.system = [{ role: "system", content: main_sys }];
        }
        else {
            chunks.system = [
                { role: "user", content: main_sys },
                { role: "assistant", content: "Ok." }
            ];
        }
        // Example messages
        for (const msg of this.gpt_prompts.example_messages) {
            chunks.examples.push({
                role: msg.role,
                content: this.fmt_system_prompt(msg.content)
            });
        }
        // Done messages (chat history)
        this.summarize_end();
        chunks.done = this.done_messages;
        // Repo and file messages
        chunks.repo = this.get_repo_messages();
        chunks.readonly_files = this.get_readonly_files_messages();
        chunks.chat_files = this.get_chat_files_messages();
        // Current messages
        chunks.cur = [...this.cur_messages];
        // System reminder
        if (this.gpt_prompts.system_reminder) {
            const reminder_message = [{
                    role: "system",
                    content: this.fmt_system_prompt(this.gpt_prompts.system_reminder)
                }];
            chunks.reminder = reminder_message;
        }
        // Add cache headers if needed
        if (this.add_cache_headers) {
            chunks.add_cache_control_headers();
        }
        return chunks;
    }
    fmt_system_prompt(prompt) {
        const final_reminders = [];
        if (this.main_model.lazy) {
            final_reminders.push(this.gpt_prompts.lazy_prompt);
        }
        if (this.main_model.overeager) {
            final_reminders.push(this.gpt_prompts.overeager_prompt);
        }
        const user_lang = this.get_user_language();
        if (user_lang) {
            final_reminders.push(`Reply in ${user_lang}.\n`);
        }
        const platform_text = this.get_platform_info();
        const language = user_lang || "the same language they are using";
        let quad_backtick_reminder = "";
        if (this.fence[0] === "````") {
            quad_backtick_reminder = "\nIMPORTANT: Use *quadruple* backticks ```` as fences, not triple backticks!\n";
        }
        const final_reminders_text = final_reminders.join("\n\n");
        return prompt
            .replace(/\{fence\}/g, `${this.fence[0]}...${this.fence[1]}`)
            .replace(/\{quad_backtick_reminder\}/g, quad_backtick_reminder)
            .replace(/\{final_reminders\}/g, final_reminders_text)
            .replace(/\{platform\}/g, platform_text)
            .replace(/\{shell_cmd_prompt\}/g, this.gpt_prompts.shell_cmd_prompt || "")
            .replace(/\{shell_cmd_reminder\}/g, this.gpt_prompts.shell_cmd_reminder || "")
            .replace(/\{go_ahead_tip\}/g, this.gpt_prompts.go_ahead_tip || "")
            .replace(/\{language\}/g, language);
    }
    get_user_language() {
        if (this.chat_language) {
            return this.normalize_language(this.chat_language);
        }
        // Simple fallback - in a real implementation this would check system locale
        return null;
    }
    normalize_language(lang_code) {
        if (!lang_code)
            return null;
        const fallback = {
            "en": "English",
            "fr": "French",
            "es": "Spanish",
            "de": "German",
            "it": "Italian",
            "pt": "Portuguese",
            "zh": "Chinese",
            "ja": "Japanese",
            "ko": "Korean",
            "ru": "Russian",
        };
        const primary_lang_code = lang_code.replace("-", "_").split("_")[0].toLowerCase();
        return fallback[primary_lang_code] || lang_code;
    }
    get_platform_info() {
        let platform_text = "";
        try {
            platform_text = `- Platform: ${process.platform}\n`;
        }
        catch {
            platform_text = "- Platform information unavailable\n";
        }
        const shell_var = process.platform === "win32" ? "COMSPEC" : "SHELL";
        const shell_val = process.env[shell_var];
        platform_text += `- Shell: ${shell_var}=${shell_val}\n`;
        const user_lang = this.get_user_language();
        if (user_lang) {
            platform_text += `- Language: ${user_lang}\n`;
        }
        const dt = new Date().toISOString().split('T')[0];
        platform_text += `- Current date: ${dt}\n`;
        if (this.repo) {
            platform_text += "- The user is operating inside a git repository\n";
        }
        return platform_text;
    }
    get_repo_messages() {
        // Placeholder - would implement repo map functionality
        return [];
    }
    get_readonly_files_messages() {
        const readonly_messages = [];
        // Handle non-image files
        const read_only_content = this.get_read_only_files_content();
        if (read_only_content) {
            readonly_messages.push({
                role: "user",
                content: this.gpt_prompts.read_only_files_prefix + read_only_content
            }, {
                role: "assistant",
                content: "Ok, I will use these files as references."
            });
        }
        return readonly_messages;
    }
    get_read_only_files_content() {
        let prompt = "";
        for (const fname of this.abs_read_only_fnames) {
            const content = this.io.read_text(fname);
            if (content !== null && !this.is_image_file(fname)) {
                const relative_fname = this.get_rel_fname(fname);
                prompt += `\n${relative_fname}\n${this.fence[0]}\n${content}${this.fence[1]}\n`;
            }
        }
        return prompt;
    }
    get_chat_files_messages() {
        const chat_files_messages = [];
        if (this.abs_fnames.size > 0) {
            const files_content = this.gpt_prompts.files_content_prefix + this.get_files_content();
            const files_reply = this.gpt_prompts.files_content_assistant_reply;
            chat_files_messages.push({ role: "user", content: files_content }, { role: "assistant", content: files_reply });
        }
        else {
            const files_content = this.gpt_prompts.files_no_full_files;
            chat_files_messages.push({ role: "user", content: files_content }, { role: "assistant", content: "Ok." });
        }
        return chat_files_messages;
    }
    get_files_content() {
        let prompt = "";
        for (const fname of this.abs_fnames) {
            const content = this.io.read_text(fname);
            if (content !== null && !this.is_image_file(fname)) {
                const relative_fname = this.get_rel_fname(fname);
                prompt += `\n${relative_fname}\n${this.fence[0]}\n${content}${this.fence[1]}\n`;
            }
        }
        return prompt;
    }
    choose_fence() {
        let all_content = "";
        for (const fname of this.abs_fnames) {
            const content = this.io.read_text(fname);
            if (content !== null) {
                all_content += content + "\n";
            }
        }
        for (const fname of this.abs_read_only_fnames) {
            const content = this.io.read_text(fname);
            if (content !== null) {
                all_content += content + "\n";
            }
        }
        const lines = all_content.split('\n');
        let good = false;
        for (const [fence_open, fence_close] of this.fences) {
            if (!lines.some(line => line.startsWith(fence_open) || line.startsWith(fence_close))) {
                this.fence = [fence_open, fence_close];
                good = true;
                break;
            }
        }
        if (!good) {
            this.fence = this.fences[0];
            this.io.tool_warning(`Unable to find a fencing strategy! Falling back to: ${this.fence[0]}...${this.fence[1]}`);
        }
    }
    is_image_file(fname) {
        const ext = path_1.default.extname(fname).toLowerCase();
        return ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp', '.pdf'].includes(ext);
    }
    check_tokens(messages) {
        if (!this.main_model.token_count) {
            return true;
        }
        const input_tokens = this.main_model.token_count(messages);
        const max_input_tokens = this.main_model.info.max_input_tokens || 0;
        if (max_input_tokens && input_tokens >= max_input_tokens) {
            this.io.tool_error(`Your estimated chat context of ${input_tokens.toLocaleString()} tokens exceeds the ` +
                `${max_input_tokens.toLocaleString()} token limit for ${this.main_model.name}!`);
            this.io.tool_output("To reduce the chat context:");
            this.io.tool_output("- Use /drop to remove unneeded files from the chat");
            this.io.tool_output("- Use /clear to clear the chat history");
            this.io.tool_output("- Break your code into smaller files");
            return this.io.confirm_ask("Try to proceed anyway?");
        }
        return true;
    }
    show_pretty() {
        if (!this.io.pretty) {
            return false;
        }
        // Only show pretty output if fences are normal triple-backtick
        return this.fence[0].startsWith("`");
    }
    show_send_output(completion) {
        this.stop_waiting_spinner();
        if (this.verbose) {
            console.log("Completion:", completion);
        }
        if (!completion.choices || completion.choices.length === 0) {
            this.io.tool_error(String(completion));
            return;
        }
        try {
            this.partial_response_content = completion.choices[0].message?.content || "";
            if (completion.choices[0].message?.tool_calls) {
                this.partial_response_function_call = completion.choices[0].message.tool_calls[0].function;
            }
        }
        catch (error) {
            this.io.tool_error(`Error processing completion: ${error}`);
            return;
        }
        const show_resp = this.render_incremental_response(true);
        this.io.assistant_output(show_resp, this.show_pretty());
        if (completion.choices[0]?.finish_reason === "length") {
            throw new FinishReasonLength();
        }
    }
    async show_send_output_stream(completion) {
        let received_content = false;
        for await (const chunk of completion) {
            if (!chunk.choices || chunk.choices.length === 0) {
                continue;
            }
            if (chunk.choices[0]?.finish_reason === "length") {
                throw new FinishReasonLength();
            }
            let text = "";
            try {
                const content = chunk.choices[0]?.delta?.content;
                if (content) {
                    text += content;
                    received_content = true;
                }
            }
            catch (error) {
                // Continue processing
            }
            if (received_content) {
                this.stop_waiting_spinner();
            }
            this.partial_response_content += text;
            if (this.show_pretty()) {
                this.live_incremental_response(false);
            }
            else if (text) {
                try {
                    process.stdout.write(text);
                }
                catch (error) {
                    // Handle encoding errors
                    process.stdout.write(text.replace(/[^\x00-\x7F]/g, "?"));
                }
            }
        }
        if (!received_content) {
            this.io.tool_warning("Empty response received from LLM. Check your provider account?");
        }
    }
    live_incremental_response(final) {
        const show_resp = this.render_incremental_response(final);
        if (this.mdstream && this.mdstream.update) {
            this.mdstream.update(show_resp, final);
        }
    }
    render_incremental_response(final) {
        return this.get_multi_response_content_in_progress();
    }
    get_multi_response_content_in_progress(final = false) {
        const cur = this.multi_response_content || "";
        let new_content = this.partial_response_content || "";
        if (new_content.endsWith(' ') && !final) {
            new_content = new_content.trimEnd();
        }
        return cur + new_content;
    }
    stop_waiting_spinner() {
        if (this.waiting_spinner) {
            try {
                this.waiting_spinner.stop();
            }
            finally {
                this.waiting_spinner = null;
            }
        }
    }
    calculate_and_show_tokens_and_cost(messages, completion) {
        let prompt_tokens = 0;
        let completion_tokens = 0;
        if (completion?.usage) {
            prompt_tokens = completion.usage.prompt_tokens || 0;
            completion_tokens = completion.usage.completion_tokens || 0;
        }
        else if (this.main_model.token_count) {
            prompt_tokens = this.main_model.token_count(messages);
            completion_tokens = this.main_model.token_count(this.partial_response_content);
        }
        this.message_tokens_sent += prompt_tokens;
        this.message_tokens_received += completion_tokens;
        let tokens_report = `Tokens: ${this.format_tokens(this.message_tokens_sent)} sent, ${this.format_tokens(this.message_tokens_received)} received.`;
        if (this.main_model.info.input_cost_per_token) {
            const cost = this.compute_costs_from_tokens(prompt_tokens, completion_tokens, 0, 0);
            this.total_cost += cost;
            this.message_cost += cost;
            const cost_report = `Cost: $${this.format_cost(this.message_cost)} message, $${this.format_cost(this.total_cost)} session.`;
            tokens_report += " " + cost_report;
        }
        this.usage_report = tokens_report;
    }
    format_tokens(value) {
        return value.toLocaleString();
    }
    format_cost(value) {
        if (value === 0)
            return "0.00";
        const magnitude = Math.abs(value);
        if (magnitude >= 0.01) {
            return value.toFixed(2);
        }
        else {
            return value.toFixed(Math.max(2, 2 - Math.floor(Math.log10(magnitude))));
        }
    }
    compute_costs_from_tokens(prompt_tokens, completion_tokens, cache_write_tokens, cache_hit_tokens) {
        let cost = 0;
        const input_cost_per_token = this.main_model.info.input_cost_per_token || 0;
        const output_cost_per_token = this.main_model.info.output_cost_per_token || 0;
        cost += prompt_tokens * input_cost_per_token;
        cost += completion_tokens * output_cost_per_token;
        return cost;
    }
    show_usage_report() {
        if (!this.usage_report) {
            return;
        }
        this.total_tokens_sent += this.message_tokens_sent;
        this.total_tokens_received += this.message_tokens_received;
        this.io.tool_output(this.usage_report);
        this.emit('message_send', {
            main_model: this.main_model,
            edit_format: this.edit_format,
            prompt_tokens: this.message_tokens_sent,
            completion_tokens: this.message_tokens_received,
            total_tokens: this.message_tokens_sent + this.message_tokens_received,
            cost: this.message_cost,
            total_cost: this.total_cost
        });
        this.message_cost = 0.0;
        this.message_tokens_sent = 0;
        this.message_tokens_received = 0;
    }
    add_assistant_reply_to_cur_messages() {
        if (this.partial_response_content) {
            this.cur_messages.push({
                role: "assistant",
                content: this.partial_response_content
            });
        }
        if (this.partial_response_function_call && Object.keys(this.partial_response_function_call).length > 0) {
            this.cur_messages.push({
                role: "assistant",
                content: null,
                function_call: this.partial_response_function_call
            });
        }
    }
    // ========================================================================
    // Edit Application
    // ========================================================================
    apply_updates() {
        const edited = new Set();
        try {
            const edits = this.get_edits();
            const prepared_edits = this.prepare_to_edit(edits);
            for (const edit of prepared_edits) {
                if (Array.isArray(edit) && edit.length >= 1) {
                    edited.add(edit[0]);
                }
            }
            this.apply_edits(prepared_edits);
        }
        catch (error) {
            this.num_malformed_responses += 1;
            this.io.tool_error("The LLM did not conform to the edit format.");
            this.io.tool_output(String(error));
            this.reflected_message = String(error);
            return edited;
        }
        for (const path of edited) {
            if (this.dry_run) {
                this.io.tool_output(`Did not apply edit to ${path} (--dry-run)`);
            }
            else {
                this.io.tool_output(`Applied edit to ${path}`);
            }
        }
        return edited;
    }
    prepare_to_edit(edits) {
        const res = [];
        const seen = new Map();
        this.need_commit_before_edits = new Set();
        for (const edit of edits) {
            let path = null;
            if (Array.isArray(edit) && edit.length > 0) {
                path = edit[0];
            }
            if (path === null) {
                res.push(edit);
                continue;
            }
            let allowed;
            if (seen.has(path)) {
                allowed = seen.get(path);
            }
            else {
                allowed = this.allowed_to_edit(path);
                seen.set(path, allowed);
            }
            if (allowed) {
                res.push(edit);
            }
        }
        this.dirty_commit();
        this.need_commit_before_edits = new Set();
        return res;
    }
    allowed_to_edit(path) {
        const full_path = this.abs_root_path(path);
        if (this.abs_fnames.has(full_path)) {
            this.check_for_dirty_commit(path);
            return true;
        }
        if (!fs_1.default.existsSync(full_path)) {
            if (!this.io.confirm_ask("Create new file?", path)) {
                this.io.tool_output(`Skipping edits to ${path}`);
                return false;
            }
            if (!this.dry_run) {
                try {
                    fs_1.default.writeFileSync(full_path, '', 'utf8');
                }
                catch (error) {
                    this.io.tool_error(`Unable to create ${path}, skipping edits.`);
                    return false;
                }
            }
            this.abs_fnames.add(full_path);
            this.check_added_files();
            return true;
        }
        if (!this.io.confirm_ask("Allow edits to file that has not been added to the chat?", path)) {
            this.io.tool_output(`Skipping edits to ${path}`);
            return false;
        }
        this.abs_fnames.add(full_path);
        this.check_added_files();
        this.check_for_dirty_commit(path);
        return true;
    }
    check_for_dirty_commit(path) {
        if (!this.repo || !this.dirty_commits) {
            return;
        }
        if (!this.repo.is_dirty(path)) {
            return;
        }
        this.io.tool_output(`Committing ${path} before applying edits.`);
        this.need_commit_before_edits.add(path);
    }
    dirty_commit() {
        if (this.need_commit_before_edits.size === 0) {
            return;
        }
        if (!this.dirty_commits || !this.repo) {
            return;
        }
        this.repo.commit({
            fnames: this.need_commit_before_edits,
            coder: this
        });
    }
    auto_commit(edited) {
        if (!this.repo || !this.auto_commits || this.dry_run) {
            return null;
        }
        const context = this.get_context_from_history(this.cur_messages);
        try {
            const res = this.repo.commit({
                fnames: edited,
                context: context,
                aider_edits: true,
                coder: this
            });
            if (res) {
                const [commit_hash, commit_message] = res;
                this.show_auto_commit_outcome(res);
                return this.gpt_prompts.files_content_gpt_edits
                    .replace('{hash}', commit_hash)
                    .replace('{message}', commit_message);
            }
            return this.gpt_prompts.files_content_gpt_no_edits;
        }
        catch (error) {
            this.io.tool_error(`Unable to commit: ${error}`);
            return null;
        }
    }
    show_auto_commit_outcome(res) {
        const [commit_hash, commit_message] = res;
        this.last_aider_commit_hash = commit_hash;
        this.aider_commit_hashes.add(commit_hash);
        if (this.show_diffs) {
            this.commands.cmd_diff();
        }
    }
    get_context_from_history(history) {
        let context = "";
        for (const msg of history) {
            context += `\n${msg.role.toUpperCase()}: ${msg.content}\n`;
        }
        return context;
    }
    // ========================================================================
    // Utility Methods
    // ========================================================================
    async reply_completed() {
        // Default implementation - subclasses can override
    }
    copy_context() {
        if (this.auto_copy_context) {
            this.commands.cmd_copy_context();
        }
    }
    async get_input() {
        const inchat_files = this.get_inchat_relative_files();
        const read_only_files = Array.from(this.abs_read_only_fnames).map(fname => this.get_rel_fname(fname));
        const all_files = [...new Set([...inchat_files, ...read_only_files])].sort();
        const edit_format = this.edit_format === this.main_model.edit_format ? "" : this.edit_format || "";
        return this.io.get_input(this.root, all_files, this.get_addable_relative_files(), this.commands, this.abs_read_only_fnames, edit_format);
    }
    check_for_file_mentions(content) {
        // Placeholder implementation
        return null;
    }
    check_for_urls(inp) {
        // Placeholder implementation - just return input unchanged
        return inp;
    }
    keyboard_interrupt() {
        const now = Date.now();
        const thresh = 2000; // 2 seconds
        if (this.last_keyboard_interrupt && now - this.last_keyboard_interrupt.getTime() < thresh) {
            this.io.tool_warning("\n\n^C KeyboardInterrupt");
            this.emit('exit', { reason: 'Control-C' });
            process.exit(0);
        }
        this.io.tool_warning("\n\n^C again to exit");
        this.last_keyboard_interrupt = new Date(now);
    }
    move_back_cur_messages(message) {
        this.done_messages.push(...this.cur_messages);
        this.summarize_start();
        if (message) {
            this.done_messages.push({ role: "user", content: message }, { role: "assistant", content: "Ok." });
        }
        this.cur_messages = [];
    }
    summarize_start() {
        // Placeholder - would implement chat history summarization
    }
    summarize_end() {
        // Placeholder - would implement chat history summarization
    }
    show_undo_hint() {
        if (this.commit_before_message.length === 0 || !this.repo) {
            return;
        }
        if (this.commit_before_message[this.commit_before_message.length - 1] !== this.repo.get_head_commit_sha()) {
            this.io.tool_output("You can use /undo to undo and discard each aider commit.");
        }
    }
    lint_edited(edited) {
        let res = "";
        for (const fname of edited) {
            if (!fname)
                continue;
            const errors = this.linter.lint(this.abs_root_path(fname));
            if (errors) {
                res += `\n${errors}\n`;
            }
        }
        if (res) {
            this.io.tool_warning(res);
        }
        return res;
    }
    run_shell_commands() {
        if (!this.suggest_shell_commands) {
            return "";
        }
        let accumulated_output = "";
        const done = new Set();
        for (const command of this.shell_commands) {
            if (done.has(command)) {
                continue;
            }
            done.add(command);
            if (this.io.confirm_ask("Run shell command?", command, undefined, false, true)) {
                this.io.tool_output(`Running ${command}`);
                // This would execute the shell command in a real implementation
                // For now, just acknowledge it
                accumulated_output += `Executed: ${command}\n`;
            }
        }
        return accumulated_output;
    }
    // ========================================================================
    // Factory Helper Methods
    // ========================================================================
    create_commands() {
        // This would return a proper Commands instance
        return {
            io: this.io,
            coder: this,
            clone: () => this.create_commands(),
            is_command: (input) => input.startsWith('/'),
            run: (input) => null,
            cmd_copy_context: () => { },
            cmd_test: (test_cmd) => null,
            cmd_diff: () => { },
            cmd_web: (url, return_content) => ""
        };
    }
    create_summarizer() {
        // This would return a proper ChatSummary instance
        return {
            too_big: (messages) => false,
            summarize: (messages) => messages,
            summarize_all: (messages) => messages
        };
    }
    create_linter() {
        // This would return a proper Linter instance
        return {
            lint: (path) => "",
            set_linter: (lang, cmd) => { }
        };
    }
}
exports.BaseCoder = BaseCoder;
