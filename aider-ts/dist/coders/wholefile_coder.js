"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WholeFileCoder = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const BaseCoder_1 = require("./BaseCoder");
const wholefile_prompts_1 = require("./wholefile_prompts");
class WholeFileCoder extends BaseCoder_1.BaseCoder {
    constructor(main_model, io, options = {}) {
        super(main_model, io, options);
        this.gpt_prompts = new wholefile_prompts_1.WholeFilePrompts();
    }
    render_incremental_response(final) {
        try {
            return this.get_edits_for_display();
        }
        catch (error) {
            return this.get_multi_response_content_in_progress();
        }
    }
    get_edits_for_display() {
        const content = this.get_multi_response_content_in_progress();
        const chat_files = this.get_inchat_relative_files();
        const output = [];
        const lines = content.split("\n");
        let saw_fname = null;
        let fname = null;
        let new_lines = [];
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (this.is_fence_line(line)) {
                if (fname !== null) {
                    // ending an existing block
                    saw_fname = null;
                    const full_path = this.abs_root_path(fname);
                    output.push(...this.do_live_diff(full_path, new_lines, true));
                    fname = null;
                    new_lines = [];
                    continue;
                }
                // fname==null ... starting a new block
                if (i > 0) {
                    fname = this.extract_filename_from_line(lines[i - 1], chat_files);
                }
                if (!fname) {
                    if (saw_fname) {
                        fname = saw_fname;
                    }
                    else if (chat_files.length === 1) {
                        fname = chat_files[0];
                    }
                    else {
                        throw new Error(`No filename provided before ${this.fence[0]} in file listing`);
                    }
                }
            }
            else if (fname !== null) {
                new_lines.push(line);
            }
            else {
                // Look for potential filenames in the text
                for (const word of line.trim().split(/\s+/)) {
                    const clean_word = word.replace(/[.,:;!]$/, "");
                    for (const chat_file of chat_files) {
                        const quoted_chat_file = `\`${chat_file}\``;
                        if (clean_word === quoted_chat_file) {
                            saw_fname = chat_file;
                        }
                    }
                }
                output.push(line);
            }
        }
        if (fname !== null) {
            // ending an existing block
            const full_path = this.abs_root_path(fname);
            output.push(...this.do_live_diff(full_path, new_lines, false));
        }
        return output.join("\n");
    }
    get_edits() {
        const content = this.get_multi_response_content_in_progress();
        const chat_files = this.get_inchat_relative_files();
        const lines = content.split("\n");
        const edits = [];
        let saw_fname = null;
        let fname = null;
        let fname_source = null;
        let new_lines = [];
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (this.is_fence_line(line)) {
                if (fname !== null) {
                    // ending an existing block
                    saw_fname = null;
                    edits.push({
                        fname,
                        fname_source: fname_source,
                        lines: [...new_lines],
                    });
                    fname = null;
                    fname_source = null;
                    new_lines = [];
                    continue;
                }
                // fname==null ... starting a new block
                if (i > 0) {
                    fname = this.extract_filename_from_line(lines[i - 1], chat_files);
                    if (fname) {
                        fname_source = "block";
                    }
                }
                if (!fname) {
                    if (saw_fname) {
                        fname = saw_fname;
                        fname_source = "saw";
                    }
                    else if (chat_files.length === 1) {
                        fname = chat_files[0];
                        fname_source = "chat";
                    }
                    else {
                        throw new Error(`No filename provided before ${this.fence[0]} in file listing`);
                    }
                }
            }
            else if (fname !== null) {
                new_lines.push(line);
            }
            else {
                // Look for potential filenames in the text
                for (const word of line.trim().split(/\s+/)) {
                    const clean_word = word.replace(/[.,:;!"]$/, "");
                    for (const chat_file of chat_files) {
                        const quoted_chat_file = `\`${chat_file}\``;
                        if (clean_word === quoted_chat_file) {
                            saw_fname = chat_file;
                        }
                    }
                }
            }
        }
        if (fname) {
            edits.push({
                fname,
                fname_source: fname_source,
                lines: [...new_lines],
            });
        }
        // Process edits by priority: block > saw > chat
        const seen = new Set();
        const refined_edits = [];
        for (const source of ["block", "saw", "chat"]) {
            for (const edit of edits) {
                if (edit.fname_source !== source) {
                    continue;
                }
                // If a higher priority source already edited the file, skip
                if (seen.has(edit.fname)) {
                    continue;
                }
                seen.add(edit.fname);
                refined_edits.push({
                    path: edit.fname,
                    fname_source: edit.fname_source,
                    lines: edit.lines,
                });
            }
        }
        return refined_edits;
    }
    apply_edits(edits) {
        for (const edit of edits) {
            const full_path = this.abs_root_path(edit.path);
            const new_content = edit.lines.join("\n");
            if (!this.dry_run) {
                this.io.write_text(full_path, new_content);
            }
        }
    }
    is_fence_line(line) {
        return this.fence.some((fence) => line.startsWith(fence));
    }
    extract_filename_from_line(line, chat_files) {
        let fname = line.trim();
        // Remove common formatting
        fname = fname.replace(/^\*+/, ""); // Remove leading asterisks
        fname = fname.replace(/\*+$/, ""); // Remove trailing asterisks
        fname = fname.replace(/:$/, ""); // Remove trailing colon
        fname = fname.replace(/^`+/, ""); // Remove leading backticks
        fname = fname.replace(/`+$/, ""); // Remove trailing backticks
        fname = fname.replace(/^#+\s*/, ""); // Remove leading hash marks
        fname = fname.trim();
        // Check if it's too long to be a reasonable filename
        if (fname.length > 250) {
            return null;
        }
        // If the filename isn't in chat_files but the basename is, use the full path
        if (fname && !chat_files.includes(fname)) {
            const basename = path.basename(fname);
            const matching_file = chat_files.find((cf) => path.basename(cf) === basename);
            if (matching_file) {
                return matching_file;
            }
        }
        return fname || null;
    }
    do_live_diff(full_path, new_lines, final) {
        if (fs.existsSync(full_path)) {
            const orig_lines = this.io.read_text(full_path);
            if (orig_lines !== null) {
                const orig_lines_array = orig_lines.split("\n");
                // This would ideally use a proper diff utility
                // For now, we'll just show the new content in a code block
                const filename = this.get_rel_fname(full_path);
                return [
                    `\`\`\`${this.get_file_language(filename)}`,
                    ...new_lines,
                    "```",
                ];
            }
        }
        // For new files, just show the content in a code block
        const filename = this.get_rel_fname(full_path);
        return [`\`\`\`${this.get_file_language(filename)}`, ...new_lines, "```"];
    }
    get_file_language(filename) {
        const ext = path.extname(filename).toLowerCase();
        const language_map = {
            ".js": "javascript",
            ".ts": "typescript",
            ".py": "python",
            ".java": "java",
            ".cpp": "cpp",
            ".c": "c",
            ".cs": "csharp",
            ".php": "php",
            ".rb": "ruby",
            ".go": "go",
            ".rs": "rust",
            ".swift": "swift",
            ".kt": "kotlin",
            ".scala": "scala",
            ".html": "html",
            ".css": "css",
            ".scss": "scss",
            ".sass": "sass",
            ".json": "json",
            ".xml": "xml",
            ".yaml": "yaml",
            ".yml": "yaml",
            ".toml": "toml",
            ".md": "markdown",
            ".sh": "bash",
            ".bash": "bash",
            ".zsh": "zsh",
            ".fish": "fish",
            ".ps1": "powershell",
            ".bat": "batch",
            ".dockerfile": "dockerfile",
            ".sql": "sql",
            ".r": "r",
            ".m": "matlab",
            ".pl": "perl",
            ".lua": "lua",
            ".vim": "vim",
        };
        return language_map[ext] || "";
    }
}
exports.WholeFileCoder = WholeFileCoder;
/**
 * A coder that operates on entire files for code modifications.
 */
WholeFileCoder.edit_format = "whole";
