"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SingleWholeFileFunctionCoder = void 0;
const BaseCoder_1 = require("./BaseCoder");
const single_wholefile_func_prompts_1 = require("./single_wholefile_func_prompts");
// TODO: Port the diffs utility
const diffs = {
    diffPartialUpdate: (origLines, lines, final, fname) => {
        console.log('diffPartialUpdate called with:', { origLines, lines, final, fname });
        return '... diff output ...';
    },
};
class SingleWholeFileFunctionCoder extends BaseCoder_1.BaseCoder {
    constructor(mainModel, io, ...args) {
        super(mainModel, io, ...args);
        this.gptPrompts = new single_wholefile_func_prompts_1.SingleWholeFileFunctionPrompts();
    }
    parse_partial_args() {
        // Placeholder implementation
        try {
            if (this.partialResponseContent) {
                const parts = this.partialResponseContent.split(/\n(explanation|content):\n/).filter(Boolean);
                const result = {};
                for (let i = 0; i < parts.length; i += 2) {
                    if (parts[i] && parts[i + 1]) {
                        result[parts[i].trim()] = parts[i + 1].trim();
                    }
                }
                return result;
            }
        }
        catch (e) { }
        return null;
    }
    addAssistantReplyToCurMessages(edited) {
        if (edited) {
            this.curMessages.push({ role: "assistant", content: this.gptPrompts.redactedEditMessage });
        }
        else {
            this.curMessages.push({ role: "assistant", content: this.partialResponseContent });
        }
    }
    renderIncrementalResponse(final = false) {
        let res = "";
        if (this.partialResponseContent) {
            res += this.partialResponseContent;
        }
        const args = this.parse_partial_args();
        if (!args) {
            return "";
        }
        for (const [k, v] of Object.entries(args)) {
            res += `\n${k}:\n${v}`;
        }
        return res;
    }
    liveDiffs(fname, content, final) {
        const lines = content.split(/\r?\n/);
        const fullPath = this.getRelFname(fname); // Assumes getRelFname provides absolute path for now
        const originalContent = this.io.read_text(fullPath);
        const origLines = originalContent ? originalContent.split('\n') : [];
        const showDiff = diffs.diffPartialUpdate(origLines, lines, final, fname).split('\n');
        return showDiff.join('\n');
    }
    getEdits() {
        const chatFiles = this.getInChatRelativeFiles();
        if (!chatFiles || chatFiles.length !== 1) {
            throw new Error(`Expected 1 chat file, but got ${chatFiles ? chatFiles.length : 0}`);
        }
        const args = this.parse_partial_args();
        if (!args || !args.content) {
            return [];
        }
        return [[chatFiles[0], args.content]];
    }
    applyEdits(edits) {
        for (const [path, content] of edits) {
            const fullPath = this.getRelFname(path); // Assumes getRelFname provides absolute path for now
            this.io.write_text(fullPath, content);
        }
    }
}
exports.SingleWholeFileFunctionCoder = SingleWholeFileFunctionCoder;
SingleWholeFileFunctionCoder.editFormat = "func";
SingleWholeFileFunctionCoder.functions = [
    {
        name: "write_file",
        description: "write new content into the file",
        parameters: {
            type: "object",
            properties: {
                explanation: {
                    type: "string",
                    description: "Step by step plan for the changes to be made to the code (future tense, markdown format)",
                },
                content: {
                    type: "string",
                    description: "Content to write to the file",
                },
            },
            required: ["explanation", "content"],
        },
    },
];
