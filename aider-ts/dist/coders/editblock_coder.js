"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditBlockCoder = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const BaseCoder_1 = require("./BaseCoder");
const editblock_prompts_1 = require("./editblock_prompts");
const difflib = __importStar(require("difflib"));
class EditBlockCoder extends BaseCoder_1.BaseCoder {
    constructor(main_model, io, options = {}) {
        super(main_model, io, options);
        this.gpt_prompts = new editblock_prompts_1.EditBlockPrompts();
    }
    get_edits() {
        const content = this.partial_response_content;
        const edits = Array.from(find_original_update_blocks(content, this.fence, this.get_inchat_relative_files()));
        this.shell_commands.push(...edits
            .filter((edit) => edit.path === null)
            .map((edit) => edit.original));
        const validEdits = edits.filter((edit) => edit.path !== null);
        return validEdits;
    }
    apply_edits_dry_run(edits) {
        return this.apply_edits(edits, true);
    }
    apply_edits(edits, dry_run = false) {
        const failed = [];
        const passed = [];
        const updated_edits = [];
        for (const edit of edits) {
            let { path: editPath, original, updated } = edit;
            if (editPath === null)
                continue;
            const full_path = this.abs_root_path(editPath);
            let new_content = null;
            if (fs.existsSync(full_path)) {
                const content = this.io.read_text(full_path);
                new_content = do_replace(full_path, content, original, updated, this.fence);
                if (!new_content && original.trim()) {
                    for (const abs_fname of this.abs_fnames) {
                        const content = this.io.read_text(abs_fname);
                        new_content = do_replace(abs_fname, content, original, updated, this.fence);
                        if (new_content) {
                            editPath = this.get_rel_fname(abs_fname);
                            break;
                        }
                    }
                }
            }
            updated_edits.push({ path: editPath, original, updated });
            if (new_content) {
                if (!dry_run) {
                    this.io.write_text(full_path, new_content);
                }
                passed.push(edit);
            }
            else {
                failed.push(edit);
            }
        }
        if (dry_run) {
            return updated_edits;
        }
        if (failed.length === 0) {
            return updated_edits;
        }
        const blocks = failed.length === 1 ? "block" : "blocks";
        let res = `# ${failed.length} SEARCH/REPLACE ${blocks} failed to match!\n`;
        for (const edit of failed) {
            const { path: editPath, original, updated } = edit;
            if (editPath === null)
                continue;
            const full_path = this.abs_root_path(editPath);
            const content = this.io.read_text(full_path);
            res += `\n## SearchReplaceNoExactMatch: This SEARCH block failed to exactly match lines in ${editPath}\n<<<<<<< SEARCH\n${original}=======\n${updated}>>>>>>> REPLACE\n\n`;
            const did_you_mean = find_similar_lines(original, content);
            if (did_you_mean) {
                res += `Did you mean to match some of these actual lines from ${editPath}?\n${this.fence[0]}\n${did_you_mean}\n${this.fence[1]}\n\n`;
            }
            if (updated && content.includes(updated)) {
                res += `Are you sure you need this SEARCH/REPLACE block?\nThe REPLACE lines are already in ${editPath}!\n\n`;
            }
            res +=
                "The SEARCH section must exactly match an existing block of lines including all white space, comments, indentation, docstrings, etc\n";
        }
        if (passed.length > 0) {
            const pblocks = passed.length === 1 ? "block" : "blocks";
            res += `\n# The other ${passed.length} SEARCH/REPLACE ${pblocks} were applied successfully.\nDon't re-send them.\nJust reply with fixed versions of the ${blocks} above that failed to match.\n`;
        }
        throw new Error(res);
    }
}
exports.EditBlockCoder = EditBlockCoder;
/**
 * A coder that uses search/replace blocks for code modifications.
 */
EditBlockCoder.edit_format = "diff";
function prep(content) {
    if (content && !content.endsWith("\n")) {
        content += "\n";
    }
    const lines = content.split("\n");
    return [content, lines];
}
function perfect_replace(whole_lines, part_lines, replace_lines) {
    const part_tup = part_lines.join("\n");
    const part_len = part_lines.length;
    for (let i = 0; i < whole_lines.length - part_len + 1; i++) {
        if (whole_lines.slice(i, i + part_len).join("\n") === part_tup) {
            const res = [
                ...whole_lines.slice(0, i),
                ...replace_lines,
                ...whole_lines.slice(i + part_len),
            ];
            return res.join("\n");
        }
    }
    return null;
}
function replace_most_similar_chunk(whole, part, replace) {
    const [whole_str, whole_lines] = prep(whole);
    const [part_str, part_lines] = prep(part);
    const [replace_str, replace_lines] = prep(replace);
    let res = perfect_or_whitespace(whole_lines, part_lines, replace_lines);
    if (res) {
        return res;
    }
    if (part_lines.length > 2 && !part_lines[0].trim()) {
        const skip_blank_line_part_lines = part_lines.slice(1);
        res = perfect_or_whitespace(whole_lines, skip_blank_line_part_lines, replace_lines);
        if (res) {
            return res;
        }
    }
    try {
        res = try_dotdotdots(whole, part, replace);
        if (res) {
            return res;
        }
    }
    catch (error) {
        // Continue to fuzzy matching
    }
    return replace_closest_edit_distance(whole_lines, part, part_lines, replace_lines);
}
function try_dotdotdots(whole, part, replace) {
    const dots_re = /(^\s*\.\.\.\n)/gm;
    const part_pieces = part.split(dots_re);
    const replace_pieces = replace.split(dots_re);
    if (part_pieces.length !== replace_pieces.length) {
        throw new Error("Unpaired ... in SEARCH/REPLACE block");
    }
    if (part_pieces.length === 1) {
        return null;
    }
    const all_dots_match = part_pieces.every((piece, i) => i % 2 === 1 ? piece === replace_pieces[i] : true);
    if (!all_dots_match) {
        throw new Error("Unmatched ... in SEARCH/REPLACE block");
    }
    const part_content = part_pieces.filter((_, i) => i % 2 === 0);
    const replace_content = replace_pieces.filter((_, i) => i % 2 === 0);
    let result = whole;
    for (let i = 0; i < part_content.length; i++) {
        const partChunk = part_content[i];
        const replaceChunk = replace_content[i];
        if (!partChunk && !replaceChunk) {
            continue;
        }
        if (!partChunk && replaceChunk) {
            if (!result.endsWith("\n")) {
                result += "\n";
            }
            result += replaceChunk;
            continue;
        }
        const occurrences = (result.match(new RegExp(escapeRegex(partChunk), "g")) || []).length;
        if (occurrences === 0) {
            throw new Error();
        }
        if (occurrences > 1) {
            throw new Error();
        }
        result = result.replace(partChunk, replaceChunk);
    }
    return result;
}
function replace_part_with_missing_leading_whitespace(whole_lines, part_lines, replace_lines) {
    const leading = [...part_lines, ...replace_lines]
        .filter((p) => p.trim())
        .map((p) => p.length - p.trimStart().length);
    if (leading.length > 0 && Math.min(...leading) > 0) {
        const num_leading = Math.min(...leading);
        part_lines = part_lines.map((p) => p.trim() ? p.substring(num_leading) : p);
        replace_lines = replace_lines.map((p) => p.trim() ? p.substring(num_leading) : p);
    }
    const num_part_lines = part_lines.length;
    for (let i = 0; i < whole_lines.length - num_part_lines + 1; i++) {
        const add_leading = match_but_for_leading_whitespace(whole_lines.slice(i, i + num_part_lines), part_lines);
        if (add_leading === null) {
            continue;
        }
        replace_lines = replace_lines.map((rline) => rline.trim() ? add_leading + rline : rline);
        const res = [
            ...whole_lines.slice(0, i),
            ...replace_lines,
            ...whole_lines.slice(i + num_part_lines),
        ];
        return res.join("\n");
    }
    return null;
}
function match_but_for_leading_whitespace(whole_lines, part_lines) {
    if (whole_lines.length !== part_lines.length) {
        return null;
    }
    if (!whole_lines.every((line, i) => line.trimStart() === part_lines[i].trimStart())) {
        return null;
    }
    const add = new Set();
    for (let i = 0; i < whole_lines.length; i++) {
        if (whole_lines[i].trim()) {
            add.add(whole_lines[i].substring(0, whole_lines[i].length - part_lines[i].length));
        }
    }
    if (add.size !== 1) {
        return null;
    }
    return add.values().next().value;
}
function replace_closest_edit_distance(whole_lines, part, part_lines, replace_lines) {
    const similarity_thresh = 0.8;
    let max_similarity = 0;
    let most_similar_chunk_start = -1;
    let most_similar_chunk_end = -1;
    const scale = 0.1;
    const min_len = Math.floor(part_lines.length * (1 - scale));
    const max_len = Math.ceil(part_lines.length * (1 + scale));
    for (let length = min_len; length <= max_len; length++) {
        for (let i = 0; i < whole_lines.length - length + 1; i++) {
            const chunk = whole_lines.slice(i, i + length).join("\n");
            const similarity = new difflib.SequenceMatcher(null, chunk, part).ratio();
            if (similarity > max_similarity) {
                max_similarity = similarity;
                most_similar_chunk_start = i;
                most_similar_chunk_end = i + length;
            }
        }
    }
    if (max_similarity < similarity_thresh) {
        return null;
    }
    const modified_whole = [
        ...whole_lines.slice(0, most_similar_chunk_start),
        ...replace_lines,
        ...whole_lines.slice(most_similar_chunk_end),
    ];
    return modified_whole.join("\n");
}
const DEFAULT_FENCE = ["``````", "``````"];
function strip_quoted_wrapping(res, fname, fence = DEFAULT_FENCE) {
    if (!res)
        return res;
    let lines = res.split("\n");
    if (fname && lines[0].trim().endsWith(path.basename(fname))) {
        lines = lines.slice(1);
    }
    if (lines.length > 1 &&
        lines[0].startsWith(fence[0]) &&
        lines[lines.length - 1].startsWith(fence[1])) {
        lines = lines.slice(1, -1);
    }
    let result = lines.join("\n");
    if (result && !result.endsWith("\n")) {
        result += "\n";
    }
    return result;
}
function do_replace(fname, content, before_text, after_text, fence) {
    const before = strip_quoted_wrapping(before_text, fname, fence);
    const after = strip_quoted_wrapping(after_text, fname, fence);
    if (!fs.existsSync(fname) && !before.trim()) {
        fs.writeFileSync(fname, "");
        content = "";
    }
    if (content === null) {
        return null;
    }
    if (!before.trim()) {
        return content + after;
    }
    else {
        return replace_most_similar_chunk(content, before, after);
    }
}
const HEAD = /^<{5,9} SEARCH>?.s*$/;
const DIVIDER = /^={5,9}\s*$/;
const UPDATED = /^>{5,9} REPLACE\s*$/;
function* find_original_update_blocks(content, fence = DEFAULT_FENCE, valid_fnames = []) {
    const lines = content.split("\n");
    let i = 0;
    let current_filename = null;
    while (i < lines.length) {
        const line = lines[i];
        const shell_starts = [
            "```bash",
            "```sh",
            "```shell",
            "```cmd",
            "```batch",
            "```powershell",
            "```ps1",
            "```zsh",
            "```fish",
            "```ksh",
            "```csh",
            "```tcsh",
        ];
        const next_is_editblock = (i + 1 < lines.length && HEAD.test(lines[i + 1].trim())) ||
            (i + 2 < lines.length && HEAD.test(lines[i + 2].trim()));
        if (shell_starts.some((start) => line.trim().startsWith(start)) &&
            !next_is_editblock) {
            const shell_content = [];
            i++;
            while (i < lines.length && !lines[i].trim().startsWith("```")) {
                shell_content.push(lines[i]);
                i++;
            }
            if (i < lines.length && lines[i].trim().startsWith("```")) {
                i++;
            }
            yield { path: null, original: shell_content.join("\n"), updated: "" };
            continue;
        }
        if (HEAD.test(line.trim())) {
            try {
                let filename = null;
                if (i + 1 < lines.length && DIVIDER.test(lines[i + 1].trim())) {
                    filename = find_filename(lines.slice(Math.max(0, i - 3), i), fence, null);
                }
                else {
                    filename = find_filename(lines.slice(Math.max(0, i - 3), i), fence, valid_fnames);
                }
                if (!filename) {
                    if (current_filename) {
                        filename = current_filename;
                    }
                    else {
                        throw new Error(`Bad/missing filename. The filename must be alone on the line before the opening fence ${fence[0]}`);
                    }
                }
                current_filename = filename;
                const original_text = [];
                i++;
                while (i < lines.length && !DIVIDER.test(lines[i].trim())) {
                    original_text.push(lines[i]);
                    i++;
                }
                if (i >= lines.length || !DIVIDER.test(lines[i].trim())) {
                    throw new Error("Expected =======");
                }
                const updated_text = [];
                i++;
                while (i < lines.length &&
                    !UPDATED.test(lines[i].trim()) &&
                    !DIVIDER.test(lines[i].trim())) {
                    updated_text.push(lines[i]);
                    i++;
                }
                if (i >= lines.length ||
                    (!UPDATED.test(lines[i].trim()) && !DIVIDER.test(lines[i].trim()))) {
                    throw new Error("Expected >>>>>>> REPLACE or =======");
                }
                yield {
                    path: filename,
                    original: original_text.join("\n"),
                    updated: updated_text.join("\n"),
                };
            }
            catch (e) {
                const processed = lines.slice(0, i + 1).join("\n");
                throw new Error(`${processed}\n^^^ ${e.message}`);
            }
        }
        i++;
    }
}
function find_filename(lines, fence, valid_fnames) {
    if (valid_fnames === null) {
        valid_fnames = [];
    }
    lines.reverse();
    lines = lines.slice(0, 3);
    const filenames = [];
    for (const line of lines) {
        const filename = strip_filename(line, fence);
        if (filename) {
            filenames.push(filename);
        }
        if (!line.startsWith(fence[0]) && !line.startsWith("```")) {
            break;
        }
    }
    if (filenames.length === 0) {
        return null;
    }
    for (const fname of filenames) {
        if (valid_fnames.includes(fname)) {
            return fname;
        }
    }
    for (const fname of filenames) {
        for (const vfn of valid_fnames) {
            if (fname === path.basename(vfn)) {
                return vfn;
            }
        }
    }
    for (const fname of filenames) {
        const close_matches = difflib.get_close_matches(fname, valid_fnames, 1, 0.8);
        if (close_matches.length === 1) {
            return close_matches[0];
        }
    }
    for (const fname of filenames) {
        if (fname.includes(".")) {
            return fname;
        }
    }
    return filenames[0];
}
function find_similar_lines(search_lines, content_lines, threshold = 0.6) {
    const searchArray = search_lines.split("\n");
    const contentArray = content_lines.split("\n");
    let best_ratio = 0;
    let best_match = null;
    let best_match_i = -1;
    for (let i = 0; i < contentArray.length - searchArray.length + 1; i++) {
        const chunk = contentArray.slice(i, i + searchArray.length);
        const ratio = new difflib.SequenceMatcher(null, searchArray, chunk).ratio();
        if (ratio > best_ratio) {
            best_ratio = ratio;
            best_match = chunk;
            best_match_i = i;
        }
    }
    if (best_ratio < threshold || best_match === null) {
        return "";
    }
    if (best_match[0] === searchArray[0] &&
        best_match[best_match.length - 1] === searchArray[searchArray.length - 1]) {
        return best_match.join("\n");
    }
    const N = 5;
    const best_match_end = Math.min(contentArray.length, best_match_i + searchArray.length + N);
    best_match_i = Math.max(0, best_match_i - N);
    const best = contentArray.slice(best_match_i, best_match_end);
    return best.join("\n");
}
function strip_filename(filename, fence) {
    let cleaned = filename.trim();
    if (cleaned === "...")
        return null;
    const start_fence = fence[0];
    if (cleaned.startsWith(start_fence)) {
        const candidate = cleaned.slice(start_fence.length);
        if (candidate && (candidate.includes(".") || candidate.includes("/")))
            return candidate;
        return null;
    }
    if (cleaned.startsWith("```")) {
        const candidate = cleaned.slice(3);
        if (candidate && (candidate.includes(".") || candidate.includes("/")))
            return candidate;
        return null;
    }
    cleaned = cleaned.replace(/:$/, "");
    cleaned = cleaned.replace(/^#+/, "");
    cleaned = cleaned.trim();
    cleaned = cleaned.replace(/`/g, "");
    cleaned = cleaned.replace(/\*/g, "");
    return cleaned;
}
function escapeRegex(str) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
