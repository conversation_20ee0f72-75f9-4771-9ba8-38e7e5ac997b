"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WholeFileFunctionCoder = exports.SingleWholeFileFunctionCoder = exports.WholeFileCoder = exports.UnifiedDiffSimpleCoder = exports.UnifiedDiffCoder = exports.PatchCoder = exports.HelpCoder = exports.EditorWholeFileCoder = exports.EditorEditBlockCoder = exports.EditorDiffFencedCoder = exports.EditBlockFencedCoder = exports.EditBlockCoder = exports.ContextCoder = exports.AskCoder = exports.ArchitectCoder = exports.BaseCoder = exports.Coder = exports.CoderRegistry = exports.coderRegistry = void 0;
const BaseCoder_1 = require("./BaseCoder");
Object.defineProperty(exports, "BaseCoder", { enumerable: true, get: function () { return BaseCoder_1.BaseCoder; } });
const architect_coder_1 = require("./architect_coder");
Object.defineProperty(exports, "ArchitectCoder", { enumerable: true, get: function () { return architect_coder_1.ArchitectCoder; } });
const ask_coder_1 = require("./ask_coder");
Object.defineProperty(exports, "AskCoder", { enumerable: true, get: function () { return ask_coder_1.AskCoder; } });
const context_coder_1 = require("./context_coder");
Object.defineProperty(exports, "ContextCoder", { enumerable: true, get: function () { return context_coder_1.ContextCoder; } });
const editblock_coder_1 = require("./editblock_coder");
Object.defineProperty(exports, "EditBlockCoder", { enumerable: true, get: function () { return editblock_coder_1.EditBlockCoder; } });
const editblock_fenced_coder_1 = require("./editblock_fenced_coder");
Object.defineProperty(exports, "EditBlockFencedCoder", { enumerable: true, get: function () { return editblock_fenced_coder_1.EditBlockFencedCoder; } });
const editor_diff_fenced_coder_1 = require("./editor_diff_fenced_coder");
Object.defineProperty(exports, "EditorDiffFencedCoder", { enumerable: true, get: function () { return editor_diff_fenced_coder_1.EditorDiffFencedCoder; } });
const editor_editblock_coder_1 = require("./editor_editblock_coder");
Object.defineProperty(exports, "EditorEditBlockCoder", { enumerable: true, get: function () { return editor_editblock_coder_1.EditorEditBlockCoder; } });
const editor_whole_coder_1 = require("./editor_whole_coder");
Object.defineProperty(exports, "EditorWholeFileCoder", { enumerable: true, get: function () { return editor_whole_coder_1.EditorWholeFileCoder; } });
const help_coder_1 = require("./help_coder");
Object.defineProperty(exports, "HelpCoder", { enumerable: true, get: function () { return help_coder_1.HelpCoder; } });
const patch_coder_1 = require("./patch_coder");
Object.defineProperty(exports, "PatchCoder", { enumerable: true, get: function () { return patch_coder_1.PatchCoder; } });
const udiff_coder_1 = require("./udiff_coder");
Object.defineProperty(exports, "UnifiedDiffCoder", { enumerable: true, get: function () { return udiff_coder_1.UnifiedDiffCoder; } });
const udiff_simple_1 = require("./udiff_simple");
Object.defineProperty(exports, "UnifiedDiffSimpleCoder", { enumerable: true, get: function () { return udiff_simple_1.UnifiedDiffSimpleCoder; } });
const wholefile_coder_1 = require("./wholefile_coder");
Object.defineProperty(exports, "WholeFileCoder", { enumerable: true, get: function () { return wholefile_coder_1.WholeFileCoder; } });
const single_wholefile_func_coder_1 = require("./single_wholefile_func_coder");
Object.defineProperty(exports, "SingleWholeFileFunctionCoder", { enumerable: true, get: function () { return single_wholefile_func_coder_1.SingleWholeFileFunctionCoder; } });
const wholefile_func_coder_1 = require("./wholefile_func_coder");
Object.defineProperty(exports, "WholeFileFunctionCoder", { enumerable: true, get: function () { return wholefile_func_coder_1.WholeFileFunctionCoder; } });
// Registry of all available coders
class CoderRegistry {
    constructor() {
        this.coders = new Map();
        this.registerDefaultCoders();
    }
    registerDefaultCoders() {
        // Register all built-in coders
        this.register(help_coder_1.HelpCoder);
        this.register(ask_coder_1.AskCoder);
        this.register(editblock_coder_1.EditBlockCoder);
        this.register(editblock_fenced_coder_1.EditBlockFencedCoder);
        this.register(wholefile_coder_1.WholeFileCoder);
        this.register(patch_coder_1.PatchCoder);
        this.register(udiff_coder_1.UnifiedDiffCoder);
        this.register(udiff_simple_1.UnifiedDiffSimpleCoder);
        this.register(architect_coder_1.ArchitectCoder);
        this.register(editor_editblock_coder_1.EditorEditBlockCoder);
        this.register(editor_whole_coder_1.EditorWholeFileCoder);
        this.register(editor_diff_fenced_coder_1.EditorDiffFencedCoder);
        this.register(context_coder_1.ContextCoder);
        this.register(single_wholefile_func_coder_1.SingleWholeFileFunctionCoder);
        this.register(wholefile_func_coder_1.WholeFileFunctionCoder);
    }
    register(CoderClass) {
        const editFormat = CoderClass.edit_format;
        if (editFormat) {
            this.coders.set(editFormat, CoderClass);
        }
    }
    get(editFormat) {
        return this.coders.get(editFormat);
    }
    getAll() {
        return Array.from(this.coders.values());
    }
    getValidFormats() {
        return Array.from(this.coders.keys()).filter(format => format !== null);
    }
    has(editFormat) {
        return this.coders.has(editFormat);
    }
    create(options = {}) {
        const main_model = options.main_model || options.from_coder?.main_model;
        if (!main_model) {
            throw new Error("main_model is required");
        }
        const io = options.io || options.from_coder?.io;
        if (!io) {
            throw new Error("io is required");
        }
        let edit_format = options.edit_format;
        if (edit_format === "code") {
            edit_format = undefined;
        }
        if (!edit_format) {
            edit_format = options.from_coder?.edit_format || main_model.edit_format;
        }
        // Handle inheritance from existing coder
        if (options.from_coder) {
            const use_kwargs = { ...options.from_coder.original_kwargs };
            // If the edit format changes, we may need to summarize chat history
            let done_messages = options.from_coder.done_messages;
            if (edit_format !== options.from_coder.edit_format &&
                done_messages.length > 0 &&
                options.summarize_from_coder !== false) {
                try {
                    if (options.from_coder.summarizer) {
                        done_messages = options.from_coder.summarizer.summarize_all(done_messages);
                    }
                }
                catch (error) {
                    io.tool_warning("Chat history summarization failed, continuing with full history");
                }
            }
            // Bring along context from the old Coder
            const update = {
                fnames: Array.from(options.from_coder.abs_fnames || []),
                read_only_fnames: Array.from(options.from_coder.abs_read_only_fnames || []),
                done_messages: done_messages,
                cur_messages: options.from_coder.cur_messages,
                aider_commit_hashes: options.from_coder.aider_commit_hashes,
                commands: options.from_coder.commands?.clone(),
                total_cost: options.from_coder.total_cost,
                ignore_mentions: options.from_coder.ignore_mentions,
                total_tokens_sent: options.from_coder.total_tokens_sent,
                total_tokens_received: options.from_coder.total_tokens_received,
                file_watcher: options.from_coder.file_watcher,
            };
            Object.assign(use_kwargs, update);
            Object.assign(use_kwargs, options);
            options = use_kwargs;
            if (options.from_coder) {
                options.from_coder.ok_to_warm_cache = false;
            }
        }
        const CoderClass = this.get(edit_format);
        if (CoderClass) {
            const coder = new CoderClass(main_model, io, options);
            coder.original_kwargs = { ...options };
            return coder;
        }
        const valid_formats = this.getValidFormats();
        throw new Error(`Unknown edit format ${edit_format}. Valid formats are: ${valid_formats.join(', ')}`);
    }
}
exports.CoderRegistry = CoderRegistry;
// Create global registry instance
exports.coderRegistry = new CoderRegistry();
// Update BaseCoder's static method to use the registry
BaseCoder_1.BaseCoder.get_all_coders = () => exports.coderRegistry.getAll();
BaseCoder_1.BaseCoder.create = (options = {}) => exports.coderRegistry.create(options);
// Legacy exports for compatibility
exports.Coder = {
    create: (options = {}) => exports.coderRegistry.create(options),
    get_all_coders: () => exports.coderRegistry.getAll(),
};
