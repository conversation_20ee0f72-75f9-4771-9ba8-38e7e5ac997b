"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDotEnv = formatDotEnv;
exports.formatYaml = formatYaml;
exports.formatMarkdown = formatMarkdown;
function formatDotEnv(actions) {
    let output = `
##########################################################
# Sample aider .env file.
# Place at the root of your git repo.
# Or use `, aider;
    --env ` to specify.
##########################################################

#################
# LLM parameters:
#
# Include xxx_API_KEY parameters and other params needed for your LLMs.
# See https://aider.chat/docs/llms.html for details.

## OpenAI
#OPENAI_API_KEY=

## Anthropic
#ANTHROPIC_API_KEY=

##...
`;
    for (const action of actions) {
        if (!action.env_var) {
            continue;
        }
        let helpText = '';
        if (action.help) {
            helpText = `## ${action.help}`;
        }
        let defaultText = '';
        if (action.default) {
            defaultText = `#{action.env_var}=${action.default}`;
        }
        else {
            defaultText = `#{action.env_var}=`;
        }
        output += `
${helpText}
${defaultText}
`;
    }
    return output;
}
function formatYaml(actions) {
    let output = `
##########################################################
# Sample .aider.conf.yml
# This file lists *all* the valid configuration entries.
# Place in your home dir, or at the root of your git repo.
##########################################################

# Note: You can only put OpenAI and Anthropic API keys in the YAML
# config file. Keys for all APIs can be stored in a .env file
# https://aider.chat/docs/config/dotenv.html

`;
    for (const action of actions) {
        let helpText = '';
        if (action.help) {
            helpText = `## ${action.help}`;
        }
        const switchName = action.name.replace(/^-+/, '');
        let defaultText = '';
        if (action.default) {
            if (typeof action.default === 'boolean') {
                defaultText = `#${switchName}: ${action.default}`;
            }
            else {
                defaultText = `#${switchName}: "${action.default}"`;
            }
        }
        else if (action.isMulti) {
            defaultText = `#${switchName}: ["xxx", "yyy"]`;
        }
        else {
            defaultText = `#${switchName}: xxx`;
        }
        output += `
${helpText}
${defaultText}
`;
    }
    return output;
}
function formatMarkdown(actions) {
    let output = '';
    for (const action of actions) {
        const switchName = action.name.replace(/^-+/, '');
        const metavar = action.metavar || 'VALUE';
        output += `### `;
        $;
        {
            switchName;
        }
        $;
        {
            metavar;
        }
        `
`;
        if (action.help) {
            output += `${action.help}  
`;
        }
        if (action.default) {
            output += `Default: ${action.default}  
`;
        }
        if (action.env_var) {
            output += `Environment variable: `;
            $;
            {
                action.env_var;
            }
            `  
`;
        }
        if (action.aliases && action.aliases.length > 0) {
            output += 'Aliases:  ;
            ';;
            for (const alias of action.aliases) {
                output += `  - `;
                $;
                {
                    alias;
                }
                $;
                {
                    metavar;
                }
                `  
`;
            }
        }
        output += ';
        ';;
    }
    return output;
}
