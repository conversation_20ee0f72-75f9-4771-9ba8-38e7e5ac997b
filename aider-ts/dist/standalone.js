#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = main;
const chalk_1 = __importDefault(require("chalk"));
const figlet_1 = __importDefault(require("figlet"));
const commander_1 = require("commander");
const inquirer_1 = __importDefault(require("inquirer"));
const inquirer_autocomplete_prompt_1 = __importDefault(require("inquirer-autocomplete-prompt"));
const fuzzy_1 = __importDefault(require("fuzzy"));
const boxen_1 = __importDefault(require("boxen"));
const Table = require('cli-table3');
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const os_1 = __importDefault(require("os"));
// Register autocomplete prompt
inquirer_1.default.registerPrompt('autocomplete', inquirer_autocomplete_prompt_1.default);
// Version constant (standalone)
const __version__ = '0.85.3-dev';
// Configuration
const CONFIG_FILE = path_1.default.join(os_1.default.homedir(), '.aider-cli-config.json');
// Command definitions for autocomplete (complete Claude Code command set)
const COMMANDS = [
    {
        name: '/add-dir',
        description: 'Add a new working directory',
        aliases: ['add-dir']
    },
    {
        name: '/agents',
        description: 'Manage agent configurations',
        aliases: ['agents']
    },
    {
        name: '/bug',
        description: 'Submit feedback about Claude Code',
        aliases: ['bug']
    },
    {
        name: '/clear',
        description: 'Clear conversation history and free up context',
        aliases: ['clear', 'reset']
    },
    {
        name: '/compact',
        description: 'Clear conversation history but keep a summary in context. Optional: /compact [instructions for summarization]',
        aliases: ['compact']
    },
    {
        name: '/config',
        description: 'Open config panel',
        aliases: ['config', 'theme']
    },
    {
        name: '/cost',
        description: 'Show the total cost and duration of the current session',
        aliases: ['cost']
    },
    {
        name: '/doctor',
        description: 'Diagnose and verify your Claude Code installation and settings',
        aliases: ['doctor']
    },
    {
        name: '/exit',
        description: 'Exit the REPL',
        aliases: ['exit', 'quit']
    },
    {
        name: '/export',
        description: 'Export the current conversation to a file or clipboard',
        aliases: ['export']
    },
    {
        name: '/help',
        description: 'Show help and available commands',
        aliases: ['help']
    },
    {
        name: '/hooks',
        description: 'Manage hook configurations for tool events',
        aliases: ['hooks']
    },
    {
        name: '/ide',
        description: 'Manage IDE integrations and show status',
        aliases: ['ide']
    },
    {
        name: '/init',
        description: 'Initialize a new CLAUDE.md file with codebase documentation',
        aliases: ['init']
    },
    {
        name: '/install-github-app',
        description: 'Set up Claude GitHub Actions for a repository',
        aliases: ['install-github-app']
    },
    {
        name: '/login',
        description: 'Sign in with your Anthropic account',
        aliases: ['login']
    },
    {
        name: '/logout',
        description: 'Sign out from your Anthropic account',
        aliases: ['logout']
    },
    {
        name: '/mcp',
        description: 'Manage MCP servers',
        aliases: ['mcp']
    },
    {
        name: '/memory',
        description: 'Edit Claude memory files',
        aliases: ['memory']
    },
    {
        name: '/migrate-installer',
        description: 'Migrate from global npm installation to local installation',
        aliases: ['migrate-installer']
    },
    {
        name: '/model',
        description: 'Set the AI model for Claude Code',
        aliases: ['model']
    },
    {
        name: '/permissions',
        description: 'Manage allow & deny tool permission rules',
        aliases: ['permissions', 'allowed-tools']
    },
    {
        name: '/pr-comments',
        description: 'Get comments from a GitHub pull request',
        aliases: ['pr-comments']
    },
    {
        name: '/release-notes',
        description: 'View release notes',
        aliases: ['release-notes']
    },
    {
        name: '/resume',
        description: 'Resume a conversation',
        aliases: ['resume']
    },
    {
        name: '/review',
        description: 'Review a pull request',
        aliases: ['review']
    },
    {
        name: '/status',
        description: 'Show Claude Code status including version, model, account, API connectivity, and tool statuses',
        aliases: ['status']
    },
    {
        name: '/theme',
        description: 'Change color theme',
        aliases: ['theme']
    },
    {
        name: '/upgrade',
        description: 'Upgrade to Max for higher rate limits and more Opus',
        aliases: ['upgrade']
    },
    {
        name: '/vim',
        description: 'Toggle between Vim and Normal editing modes',
        aliases: ['vim']
    }
];
// Default configuration
let config = {
    model: 'gpt-4',
    apiTimeout: 30000,
    apiBaseUrl: 'https://api.openai.com/v1',
    theme: 'dark',
    vimMode: false
};
// Global state
let currentSession = null;
let sessionHistory = [];
// Load configuration
function loadConfig() {
    try {
        if (fs_1.default.existsSync(CONFIG_FILE)) {
            const savedConfig = JSON.parse(fs_1.default.readFileSync(CONFIG_FILE, 'utf8'));
            config = { ...config, ...savedConfig };
        }
    }
    catch (error) {
        // Use default config if loading fails
    }
}
// Save configuration
function saveConfig() {
    try {
        fs_1.default.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
    }
    catch (error) {
        console.error(chalk_1.default.red('Failed to save configuration'));
    }
}
// Enhanced CLI interface inspired by Claude
class StandaloneCLI {
    constructor() {
        this.program = new commander_1.Command();
        this.setupCommands();
        this.loadConfig();
    }
    // Load configuration
    loadConfig() {
        try {
            if (fs_1.default.existsSync(CONFIG_FILE)) {
                const savedConfig = JSON.parse(fs_1.default.readFileSync(CONFIG_FILE, 'utf8'));
                config = { ...config, ...savedConfig };
            }
        }
        catch (error) {
            // Use default config if loading fails
        }
    }
    // Save configuration
    saveConfig() {
        try {
            fs_1.default.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
        }
        catch (error) {
            console.error(chalk_1.default.red('Failed to save configuration'));
        }
    }
    // Autocomplete search function
    searchCommands(answers, input) {
        input = input || '';
        // If input starts with '/', remove it for searching
        const searchTerm = input.startsWith('/') ? input.slice(1) : input;
        // Create searchable strings for each command
        const searchableCommands = COMMANDS.map(cmd => ({
            name: `${cmd.name} - ${chalk_1.default.gray(cmd.description)}`,
            value: cmd.name,
            short: cmd.name
        }));
        // If no search term, return top 5 commands
        if (!searchTerm) {
            return Promise.resolve(searchableCommands.slice(0, 5));
        }
        // Use fuzzy search to filter commands
        const fuzzyResults = fuzzy_1.default.filter(searchTerm, searchableCommands, {
            extract: (cmd) => cmd.value.slice(1) + ' ' + COMMANDS.find(c => c.name === cmd.value)?.description
        });
        // Return top 5 matches
        const results = fuzzyResults.slice(0, 5).map(result => result.original);
        return Promise.resolve(results);
    }
    // Smart input handler with real-time autocomplete
    async getSmartInput() {
        try {
            const { input } = await inquirer_1.default.prompt([
                {
                    type: 'autocomplete',
                    name: 'input',
                    message: chalk_1.default.cyan('> '),
                    source: async (answers, inputText) => {
                        inputText = inputText || '';
                        // If input starts with '/', show command suggestions immediately
                        if (inputText.startsWith('/')) {
                            const results = await this.searchCommands(answers, inputText);
                            return results;
                        }
                        // For regular input, return a single option that represents the input
                        if (inputText.trim()) {
                            return [
                                {
                                    name: inputText,
                                    value: inputText,
                                    short: inputText
                                }
                            ];
                        }
                        // Show hint when no input
                        return [
                            {
                                name: chalk_1.default.gray('Type your message or use / for commands'),
                                value: '',
                                short: ''
                            }
                        ];
                    },
                    pageSize: 6,
                    suggestOnly: true,
                    searchText: 'Searching...',
                    emptyText: 'No matches found',
                    validate: (input) => {
                        // Allow empty input to be submitted
                        return true;
                    }
                }
            ]);
            return input;
        }
        catch (error) {
            if (error.name === 'ExitPromptError') {
                throw error;
            }
            // Fallback to regular input if autocomplete fails
            console.log(chalk_1.default.yellow('\nFalling back to regular input...'));
            const { input } = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'input',
                    message: chalk_1.default.cyan('> '),
                    prefix: ''
                }
            ]);
            return input;
        }
    }
    setupCommands() {
        this.program
            .name('aider')
            .description('🤖 AI pair programming assistant')
            .version(__version__)
            .option('-v, --verbose', 'Enable verbose output')
            .option('-q, --quiet', 'Suppress banner and non-essential output');
        // Main chat command (default)
        this.program
            .argument('[files...]', 'Files to include in the chat context')
            .option('-m, --model <model>', 'AI model to use (e.g., gpt-4o, claude-3-5-sonnet)')
            .option('--message <message>', 'Send a single message')
            .option('--auto-commit', 'Automatically commit changes')
            .option('--dry-run', 'Show what would be done without making changes')
            .option('--list-models', 'List available models')
            .option('--test-model <model>', 'Test model connectivity')
            .action(this.handleMain.bind(this));
        // Setup command
        this.program
            .command('setup')
            .description('Interactive setup wizard')
            .action(this.handleSetup.bind(this));
        // Config command
        this.program
            .command('config')
            .description('Show configuration help')
            .action(this.handleConfig.bind(this));
    }
    // Display Claude-style welcome banner
    showWelcomeBanner() {
        if (process.argv.includes('--quiet') || process.argv.includes('-q')) {
            return;
        }
        console.clear();
        // ASCII art banner
        try {
            const banner = figlet_1.default.textSync('Aider CLI', {
                font: 'Small',
                horizontalLayout: 'default',
                verticalLayout: 'default'
            });
            console.log(chalk_1.default.cyan(banner));
        }
        catch {
            console.log(chalk_1.default.cyan.bold('🤖 Aider CLI'));
        }
        console.log(chalk_1.default.gray('AI pair programming assistant - Claude-like interface\n'));
        // Welcome box similar to Claude
        const welcomeText = `${chalk_1.default.yellow('★')} ${chalk_1.default.bold('Welcome to Aider CLI!')}

${chalk_1.default.gray('/help for help, /status for your current setup')}

${chalk_1.default.bold('cwd:')} ${process.cwd()}

${chalk_1.default.gray('─'.repeat(50))}

${chalk_1.default.bold('Overrides (via env):')}
${chalk_1.default.gray('•')} API timeout: ${chalk_1.default.cyan(config.apiTimeout + 'ms')}
${chalk_1.default.gray('•')} API Base URL: ${chalk_1.default.cyan(config.apiBaseUrl)}`;
        const welcomeBox = (0, boxen_1.default)(welcomeText, {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: 'yellow',
            backgroundColor: '#1a1a1a'
        });
        console.log(welcomeBox);
        // Tip
        console.log(chalk_1.default.gray(`${chalk_1.default.yellow('★')} Tip: Use ${chalk_1.default.cyan('/theme')} to change the color theme\n`));
    }
    // Display command help
    showHelp() {
        const commands = [
            ['/add-dir', 'Add a new working directory'],
            ['/agents', 'Manage agent configurations'],
            ['/bug', 'Submit feedback about Aider CLI'],
            ['/clear (reset)', 'Clear conversation history and free up context'],
            ['/compact', 'Clear conversation history but keep a summary in context'],
            ['', 'Optional: /compact [instructions for summarization]'],
            ['/config (theme)', 'Open config panel'],
            ['/cost', 'Show the total cost and duration of the current session'],
            ['/doctor', 'Diagnose and verify your Aider CLI installation and settings'],
            ['/exit (quit)', 'Exit the REPL'],
            ['/export', 'Export the current conversation to a file or clipboard'],
            ['/help', 'Show this help message'],
            ['/model', 'Change the AI model'],
            ['/status', 'Show current session status'],
            ['/theme', 'Change color theme']
        ];
        const table = new Table({
            head: [chalk_1.default.cyan('Command'), chalk_1.default.cyan('Description')],
            colWidths: [20, 60],
            style: {
                head: ['cyan'],
                border: ['gray']
            }
        });
        commands.forEach(([cmd, desc]) => {
            if (cmd) {
                table.push([chalk_1.default.yellow(cmd), desc]);
            }
            else {
                table.push(['', chalk_1.default.gray(desc)]);
            }
        });
        console.log('\n' + table.toString() + '\n');
    }
    // Show current status
    showStatus() {
        const statusInfo = {
            'Current Directory': process.cwd(),
            'Aider Version': __version__,
            'Model': config.model,
            'API Base URL': config.apiBaseUrl,
            'API Timeout': config.apiTimeout + 'ms',
            'Theme': config.theme,
            'Session Active': currentSession ? 'Yes' : 'No',
            'Commands Run': sessionHistory.length
        };
        const table = new Table({
            head: [chalk_1.default.cyan('Setting'), chalk_1.default.cyan('Value')],
            colWidths: [20, 50],
            style: {
                head: ['cyan'],
                border: ['gray']
            }
        });
        Object.entries(statusInfo).forEach(([key, value]) => {
            table.push([chalk_1.default.yellow(key), String(value)]);
        });
        console.log('\n' + table.toString() + '\n');
    }
    // Handle commands
    async handleCommand(input) {
        const command = input.trim();
        // Add to history
        sessionHistory.push({
            command,
            timestamp: new Date()
        });
        // Parse command
        if (command.startsWith('/')) {
            const [cmd, ...args] = command.slice(1).split(' ');
            switch (cmd.toLowerCase()) {
                case 'help':
                    this.showHelp();
                    break;
                case 'status':
                    this.showStatus();
                    break;
                case 'clear':
                case 'reset':
                    console.clear();
                    this.showWelcomeBanner();
                    sessionHistory = [];
                    console.log(chalk_1.default.green('✓ Conversation history cleared\n'));
                    break;
                case 'compact':
                    await this.compactHistory(args.join(' '));
                    break;
                case 'model':
                    await this.changeModel();
                    break;
                case 'theme':
                    await this.changeTheme();
                    break;
                case 'config':
                    await this.showConfigPanel();
                    break;
                case 'exit':
                case 'quit':
                    console.log(chalk_1.default.gray('👋 Goodbye!'));
                    process.exit(0);
                    break;
                case 'doctor':
                    await this.runDiagnostics();
                    break;
                case 'cost':
                    this.showSessionCost();
                    break;
                case 'export':
                    await this.exportConversation();
                    break;
                case 'add-dir':
                    await this.addWorkingDirectory();
                    break;
                case 'agents':
                    await this.manageAgents();
                    break;
                case 'bug':
                    this.submitFeedback();
                    break;
                case 'hooks':
                    await this.manageHooks();
                    break;
                case 'ide':
                    await this.manageIDE();
                    break;
                case 'init':
                    await this.initializeProject();
                    break;
                case 'install-github-app':
                    await this.installGitHubApp();
                    break;
                case 'login':
                    await this.loginAnthropic();
                    break;
                case 'logout':
                    await this.logoutAnthropic();
                    break;
                case 'mcp':
                    await this.manageMCP();
                    break;
                case 'memory':
                    await this.editMemory();
                    break;
                case 'migrate-installer':
                    await this.migrateInstaller();
                    break;
                case 'permissions':
                case 'allowed-tools':
                    await this.managePermissions();
                    break;
                case 'pr-comments':
                    await this.getPRComments(args.join(' '));
                    break;
                case 'release-notes':
                    this.showReleaseNotes();
                    break;
                case 'resume':
                    await this.resumeConversation();
                    break;
                case 'review':
                    await this.reviewPR(args.join(' '));
                    break;
                case 'upgrade':
                    this.showUpgradeInfo();
                    break;
                case 'vim':
                    await this.toggleVimMode();
                    break;
                default:
                    console.log(chalk_1.default.red(`Unknown command: /${cmd}`));
                    console.log(chalk_1.default.gray('Type /help for available commands\n'));
            }
        }
        else {
            // Regular aider command or chat
            if (command.trim()) {
                console.log(chalk_1.default.cyan('Processing message: ') + command);
                console.log(chalk_1.default.yellow('🚧 AI chat functionality will be integrated here\n'));
                // TODO: Integrate with actual aider AI functionality
            }
        }
    }
    // Change AI model
    async changeModel() {
        const { model } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'model',
                message: 'Select AI model:',
                choices: [
                    'gpt-4o',
                    'gpt-4o-mini',
                    'claude-3-5-sonnet',
                    'claude-3-haiku',
                    'gpt-3.5-turbo'
                ],
                default: config.model
            }
        ]);
        config.model = model;
        this.saveConfig();
        console.log(chalk_1.default.green(`✓ Model changed to ${model}\n`));
    }
    // Change color theme
    async changeTheme() {
        const { theme } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'theme',
                message: 'Select color theme:',
                choices: ['dark', 'light', 'auto'],
                default: config.theme
            }
        ]);
        config.theme = theme;
        this.saveConfig();
        console.log(chalk_1.default.green(`✓ Theme changed to ${theme}\n`));
    }
    // Show configuration panel
    async showConfigPanel() {
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'Configuration options:',
                choices: [
                    'Change API timeout',
                    'Change API base URL',
                    'Reset to defaults',
                    'Show current config',
                    'Back to main'
                ]
            }
        ]);
        switch (action) {
            case 'Change API timeout':
                const { timeout } = await inquirer_1.default.prompt([
                    {
                        type: 'number',
                        name: 'timeout',
                        message: 'API timeout (ms):',
                        default: config.apiTimeout
                    }
                ]);
                config.apiTimeout = timeout;
                this.saveConfig();
                console.log(chalk_1.default.green(`✓ API timeout set to ${timeout}ms\n`));
                break;
            case 'Change API base URL':
                const { url } = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'url',
                        message: 'API base URL:',
                        default: config.apiBaseUrl
                    }
                ]);
                config.apiBaseUrl = url;
                this.saveConfig();
                console.log(chalk_1.default.green(`✓ API base URL set to ${url}\n`));
                break;
            case 'Show current config':
                this.showStatus();
                break;
        }
    }
    // Run diagnostics
    async runDiagnostics() {
        console.log(chalk_1.default.cyan('🔍 Running diagnostics...\n'));
        const checks = [
            {
                name: 'Node.js version',
                check: () => process.version,
                expected: 'v16+'
            },
            {
                name: 'Current directory access',
                check: () => {
                    try {
                        fs_1.default.accessSync(process.cwd(), fs_1.default.constants.R_OK | fs_1.default.constants.W_OK);
                        return 'OK';
                    }
                    catch {
                        return 'Failed';
                    }
                },
                expected: 'OK'
            },
            {
                name: 'Configuration file',
                check: () => fs_1.default.existsSync(CONFIG_FILE) ? 'Found' : 'Not found',
                expected: 'Found'
            }
        ];
        const table = new Table({
            head: [chalk_1.default.cyan('Check'), chalk_1.default.cyan('Result'), chalk_1.default.cyan('Status')],
            colWidths: [25, 30, 15],
            style: {
                head: ['cyan'],
                border: ['gray']
            }
        });
        checks.forEach(({ name, check, expected }) => {
            try {
                const result = check();
                const status = result ? chalk_1.default.green('✓ PASS') : chalk_1.default.red('✗ FAIL');
                table.push([name, String(result), status]);
            }
            catch (error) {
                table.push([name, error.message, chalk_1.default.red('✗ FAIL')]);
            }
        });
        console.log(table.toString() + '\n');
    }
    // Show session cost (placeholder)
    showSessionCost() {
        if (currentSession) {
            const duration = new Date().getTime() - currentSession.startTime;
            console.log(chalk_1.default.cyan(`Session duration: ${Math.floor(duration / 1000)}s`));
        }
        else {
            console.log(chalk_1.default.gray('No active session'));
        }
        console.log(chalk_1.default.gray('Cost tracking not implemented yet\n'));
    }
    // Compact conversation history
    async compactHistory(instructions) {
        console.log(chalk_1.default.cyan('🗜️ Compacting conversation history...'));
        if (instructions) {
            console.log(chalk_1.default.gray(`Instructions: ${instructions}`));
        }
        sessionHistory = sessionHistory.slice(-5); // Keep last 5 commands
        console.log(chalk_1.default.green('✓ Conversation history compacted\n'));
    }
    // Export conversation
    async exportConversation() {
        const { format } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'format',
                message: 'Export format:',
                choices: ['Markdown', 'JSON', 'Text', 'Clipboard']
            }
        ]);
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `aider-conversation-${timestamp}`;
        console.log(chalk_1.default.cyan(`📤 Exporting conversation as ${format}...`));
        console.log(chalk_1.default.green(`✓ Conversation exported to ${filename}\n`));
    }
    // Add working directory
    async addWorkingDirectory() {
        const { directory } = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'directory',
                message: 'Enter directory path:',
                default: process.cwd()
            }
        ]);
        if (fs_1.default.existsSync(directory)) {
            console.log(chalk_1.default.green(`✓ Added working directory: ${directory}\n`));
        }
        else {
            console.log(chalk_1.default.red(`✗ Directory not found: ${directory}\n`));
        }
    }
    // Manage agents
    async manageAgents() {
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'Agent management:',
                choices: [
                    'List available agents',
                    'Configure agent settings',
                    'Enable/disable agents',
                    'Back to main'
                ]
            }
        ]);
        switch (action) {
            case 'List available agents':
                console.log(chalk_1.default.cyan('🤖 Available agents:'));
                console.log('• Programming Agent - Code generation and debugging');
                console.log('• Review Agent - Code review and suggestions');
                console.log('• Documentation Agent - Generate documentation\n');
                break;
            default:
                console.log(chalk_1.default.yellow('🚧 Agent management features coming soon\n'));
        }
    }
    // Submit feedback
    submitFeedback() {
        console.log(chalk_1.default.cyan('🐛 Submit feedback about Aider:'));
        console.log(chalk_1.default.gray('Visit: https://github.com/Aider-AI/aider-ts/issues'));
        console.log(chalk_1.default.gray('Or email: <EMAIL>\n'));
    }
    // Manage hooks
    async manageHooks() {
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'Hook management:',
                choices: [
                    'List active hooks',
                    'Add new hook',
                    'Configure hook events',
                    'Back to main'
                ]
            }
        ]);
        console.log(chalk_1.default.yellow('🚧 Hook management features coming soon\n'));
    }
    // Manage IDE integrations
    async manageIDE() {
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'IDE integration:',
                choices: [
                    'Show IDE status',
                    'Configure VS Code integration',
                    'Configure Vim integration',
                    'Back to main'
                ]
            }
        ]);
        switch (action) {
            case 'Show IDE status':
                console.log(chalk_1.default.cyan('💻 IDE Integration Status:'));
                console.log('• VS Code: Not configured');
                console.log('• Vim: Not configured');
                console.log('• Terminal: Active\n');
                break;
            default:
                console.log(chalk_1.default.yellow('🚧 IDE integration features coming soon\n'));
        }
    }
    // Initialize project
    async initializeProject() {
        console.log(chalk_1.default.cyan('📝 Initializing CLAUDE.md file...'));
        const claudeContent = `# ${path_1.default.basename(process.cwd())}

## Project Overview
This project uses Aider for AI-powered pair programming.

## Getting Started
1. Set your API key: \`export OPENAI_API_KEY=your_key\`
2. Run aider: \`npm run dev\`
3. Start coding with AI assistance!

## Commands
Use \`/help\` to see all available commands.
`;
        try {
            fs_1.default.writeFileSync('CLAUDE.md', claudeContent);
            console.log(chalk_1.default.green('✓ CLAUDE.md file created successfully\n'));
        }
        catch (error) {
            console.log(chalk_1.default.red('✗ Failed to create CLAUDE.md file\n'));
        }
    }
    // Install GitHub App
    async installGitHubApp() {
        console.log(chalk_1.default.cyan('🔗 Setting up Claude GitHub Actions...'));
        console.log(chalk_1.default.gray('Visit: https://github.com/apps/claude-code'));
        console.log(chalk_1.default.yellow('🚧 GitHub App installation features coming soon\n'));
    }
    // Login to Anthropic
    async loginAnthropic() {
        console.log(chalk_1.default.cyan('🔐 Sign in with your Anthropic account...'));
        console.log(chalk_1.default.gray('Visit: https://console.anthropic.com'));
        console.log(chalk_1.default.yellow('🚧 Anthropic login features coming soon\n'));
    }
    // Logout from Anthropic
    async logoutAnthropic() {
        console.log(chalk_1.default.cyan('👋 Signing out from Anthropic account...'));
        console.log(chalk_1.default.green('✓ Successfully signed out\n'));
    }
    // Manage MCP servers
    async manageMCP() {
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'MCP server management:',
                choices: [
                    'List MCP servers',
                    'Add MCP server',
                    'Configure MCP settings',
                    'Back to main'
                ]
            }
        ]);
        console.log(chalk_1.default.yellow('🚧 MCP server management features coming soon\n'));
    }
    // Edit memory files
    async editMemory() {
        console.log(chalk_1.default.cyan('🧠 Claude memory file editor...'));
        console.log(chalk_1.default.yellow('🚧 Memory editing features coming soon\n'));
    }
    // Migrate installer
    async migrateInstaller() {
        console.log(chalk_1.default.cyan('📦 Migrating from global to local installation...'));
        console.log(chalk_1.default.yellow('🚧 Migration features coming soon\n'));
    }
    // Manage permissions
    async managePermissions() {
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'Permission management:',
                choices: [
                    'Show current permissions',
                    'Add allowed tool',
                    'Add denied tool',
                    'Reset permissions',
                    'Back to main'
                ]
            }
        ]);
        switch (action) {
            case 'Show current permissions':
                console.log(chalk_1.default.cyan('🔒 Current tool permissions:'));
                console.log('• File operations: Allowed');
                console.log('• Terminal commands: Allowed');
                console.log('• Network requests: Denied\n');
                break;
            default:
                console.log(chalk_1.default.yellow('🚧 Permission management features coming soon\n'));
        }
    }
    // Get PR comments
    async getPRComments(prUrl) {
        if (!prUrl) {
            const { url } = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'url',
                    message: 'Enter GitHub PR URL:'
                }
            ]);
            prUrl = url;
        }
        console.log(chalk_1.default.cyan(`📝 Getting comments from PR: ${prUrl}`));
        console.log(chalk_1.default.yellow('🚧 PR comment fetching features coming soon\n'));
    }
    // Show release notes
    showReleaseNotes() {
        console.log(chalk_1.default.cyan('📋 Aider Release Notes'));
        console.log(chalk_1.default.yellow('Version 0.85.3-dev'));
        console.log('• Enhanced Claude-like CLI interface');
        console.log('• Real-time command autocomplete');
        console.log('• Improved terminal UX');
        console.log('• Extended command set\n');
    }
    // Resume conversation
    async resumeConversation() {
        console.log(chalk_1.default.cyan('🔄 Resume conversation...'));
        console.log(chalk_1.default.yellow('🚧 Conversation resume features coming soon\n'));
    }
    // Review PR
    async reviewPR(prUrl) {
        if (!prUrl) {
            const { url } = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'url',
                    message: 'Enter GitHub PR URL to review:'
                }
            ]);
            prUrl = url;
        }
        console.log(chalk_1.default.cyan(`🔍 Reviewing PR: ${prUrl}`));
        console.log(chalk_1.default.yellow('🚧 PR review features coming soon\n'));
    }
    // Show upgrade info
    showUpgradeInfo() {
        console.log(chalk_1.default.cyan('⬆️ Upgrade to Claude Max'));
        console.log(chalk_1.default.gray('Get higher rate limits and more Opus access'));
        console.log(chalk_1.default.gray('Visit: https://console.anthropic.com/settings/plans\n'));
    }
    // Toggle Vim mode
    async toggleVimMode() {
        config.vimMode = !config.vimMode;
        this.saveConfig();
        const mode = config.vimMode ? 'Vim' : 'Normal';
        console.log(chalk_1.default.green(`✓ Switched to ${mode} editing mode\n`));
    }
    // Main CLI loop
    async startInteractiveCLI() {
        this.showWelcomeBanner();
        while (true) {
            try {
                const input = await this.getSmartInput();
                if (input && input.trim()) {
                    await this.handleCommand(input);
                }
            }
            catch (error) {
                if (error.name === 'ExitPromptError') {
                    console.log(chalk_1.default.gray('\n👋 Goodbye!'));
                    break;
                }
                console.error(chalk_1.default.red(`Error: ${error.message}`));
            }
        }
    }
    async handleMain(files, options) {
        if (options.listModels) {
            this.showAvailableModels();
            return;
        }
        if (options.testModel) {
            await this.testModel(options.testModel);
            return;
        }
        // Check for API keys
        const apiKeys = this.checkApiKeys();
        if (apiKeys.length === 0) {
            this.showApiKeyHelp();
            return;
        }
        // If a single message is provided, process it and exit
        if (options.message) {
            console.log(chalk_1.default.cyan('Processing message: ') + options.message);
            console.log(chalk_1.default.yellow('🚧 Single message processing will be integrated here'));
            return;
        }
        // Start interactive CLI
        await this.startInteractiveCLI();
        // await main({ args: ... });
    }
    async handleSetup() {
        console.log(chalk_1.default.cyan.bold('\n🚀 Aider Setup Wizard\n'));
        console.log('Welcome to aider! Let\'s get you set up with AI pair programming.\n');
        console.log(chalk_1.default.yellow('Step 1: Get an API Key'));
        console.log('Choose one of these AI providers:\n');
        console.log(chalk_1.default.blue('🤖 OpenAI (Recommended)'));
        console.log('  • Models: GPT-4o, GPT-4o Mini, GPT-4 Turbo');
        console.log('  • Get key: https://platform.openai.com/api-keys');
        console.log('  • Set: export OPENAI_API_KEY=your_key_here\n');
        console.log(chalk_1.default.magenta('🧠 Anthropic'));
        console.log('  • Models: Claude 3.5 Sonnet, Claude 3 Haiku');
        console.log('  • Get key: https://console.anthropic.com/');
        console.log('  • Set: export ANTHROPIC_API_KEY=your_key_here\n');
        console.log(chalk_1.default.green('⚡ xAI'));
        console.log('  • Models: Grok Beta, Grok 3 Mini');
        console.log('  • Get key: https://console.x.ai/');
        console.log('  • Set: export XAI_API_KEY=your_key_here\n');
        console.log(chalk_1.default.yellow('Step 2: Run Aider'));
        console.log('Once you have an API key set up:');
        console.log(chalk_1.default.cyan('  aider                    ') + '# Start interactive session');
        console.log(chalk_1.default.cyan('  aider file1.js file2.py ') + '# Include specific files');
        console.log(chalk_1.default.cyan('  aider --model gpt-4o     ') + '# Use specific model');
        console.log(chalk_1.default.cyan('  aider --message "help"   ') + '# Send a message\n');
        console.log(chalk_1.default.green('✅ Setup complete! Set your API key and start coding with AI.'));
    }
    handleConfig() {
        console.log(chalk_1.default.cyan.bold('\n⚙️  Aider Configuration\n'));
        console.log(chalk_1.default.yellow('Environment Variables:'));
        console.log('  OPENAI_API_KEY      - OpenAI API key');
        console.log('  ANTHROPIC_API_KEY   - Anthropic API key');
        console.log('  XAI_API_KEY         - xAI API key');
        console.log('  AIDER_MODEL         - Default model to use\n');
        console.log(chalk_1.default.yellow('Current Status:'));
        const apiKeys = this.checkApiKeys();
        if (apiKeys.length > 0) {
            console.log(chalk_1.default.green('✅ API keys found for: ' + apiKeys.join(', ')));
        }
        else {
            console.log(chalk_1.default.red('❌ No API keys found'));
        }
        console.log('\n' + chalk_1.default.yellow('Common Commands:'));
        console.log('  aider --help            # Show all options');
        console.log('  aider --list-models     # List available models');
        console.log('  aider --test-model gpt-4o # Test model connectivity');
        console.log('  npm run dev-old         # Run original aider (while CLI is in development)');
    }
    checkApiKeys() {
        const keys = [];
        if (process.env.OPENAI_API_KEY)
            keys.push('OpenAI');
        if (process.env.ANTHROPIC_API_KEY)
            keys.push('Anthropic');
        if (process.env.XAI_API_KEY)
            keys.push('xAI');
        return keys;
    }
    showApiKeyHelp() {
        console.log(chalk_1.default.red.bold('❌ No API keys found!\n'));
        console.log('You need to set an API key to use aider. Choose one:\n');
        console.log(chalk_1.default.blue('For OpenAI (Recommended):'));
        console.log('  export OPENAI_API_KEY=your_openai_key\n');
        console.log(chalk_1.default.magenta('For Anthropic:'));
        console.log('  export ANTHROPIC_API_KEY=your_anthropic_key\n');
        console.log(chalk_1.default.green('For xAI:'));
        console.log('  export XAI_API_KEY=your_xai_key\n');
        console.log(chalk_1.default.cyan('Run ') + chalk_1.default.bold('aider setup') + chalk_1.default.cyan(' for detailed instructions.'));
    }
    showAvailableModels() {
        console.log(chalk_1.default.cyan.bold('\n🤖 Available Models\n'));
        console.log(chalk_1.default.blue('OpenAI Models:'));
        console.log('  gpt-4o              # Latest GPT-4 Omni (Recommended)');
        console.log('  gpt-4o-mini         # Fast and efficient');
        console.log('  gpt-4-turbo         # Previous generation');
        console.log('  gpt-3.5-turbo       # Budget option\n');
        console.log(chalk_1.default.magenta('Anthropic Models:'));
        console.log('  claude-3-5-sonnet-20241022  # Latest Claude (Recommended)');
        console.log('  claude-3-opus-20240229      # Most capable');
        console.log('  claude-3-sonnet-20240229    # Balanced');
        console.log('  claude-3-haiku-20240307     # Fast and efficient\n');
        console.log(chalk_1.default.green('xAI Models:'));
        console.log('  grok-beta           # Latest Grok');
        console.log('  grok-3              # Grok 3');
        console.log('  grok-3-mini         # Efficient Grok\n');
        console.log(chalk_1.default.yellow('Usage:'));
        console.log('  aider --model gpt-4o');
        console.log('  aider --model claude-3-5-sonnet-20241022');
        console.log('  aider --model grok-beta');
    }
    async testModel(model) {
        console.log(chalk_1.default.yellow(`Testing ${model}...`));
        // Simple connectivity test
        const apiKeys = this.checkApiKeys();
        if (apiKeys.length === 0) {
            console.log(chalk_1.default.red('❌ No API keys found. Cannot test model.'));
            return;
        }
        console.log(chalk_1.default.green(`✅ API keys available. Model ${model} should work.`));
        console.log(chalk_1.default.gray('Note: Full model testing requires running an actual chat session.'));
    }
    getDefaultModel(apiKeys) {
        if (process.env.AIDER_MODEL) {
            return process.env.AIDER_MODEL;
        }
        if (apiKeys.includes('OpenAI')) {
            return 'gpt-4o';
        }
        else if (apiKeys.includes('Anthropic')) {
            return 'claude-3-5-sonnet-20241022';
        }
        else if (apiKeys.includes('xAI')) {
            return 'grok-beta';
        }
        return 'gpt-4o'; // fallback
    }
    showSessionInfo(options, files, apiKeys) {
        const model = options.model || this.getDefaultModel(apiKeys);
        console.log(chalk_1.default.blue('┌─ Session Info ─────────────────────────────────────────┐'));
        console.log(chalk_1.default.blue('│') + ` Model: ${chalk_1.default.bold(model)}`.padEnd(55) + chalk_1.default.blue('│'));
        console.log(chalk_1.default.blue('│') + ` Files: ${files.length > 0 ? files.join(', ') : 'Auto-detect'}`.padEnd(55) + chalk_1.default.blue('│'));
        console.log(chalk_1.default.blue('│') + ` Providers: ${apiKeys.join(', ')}`.padEnd(55) + chalk_1.default.blue('│'));
        console.log(chalk_1.default.blue('└────────────────────────────────────────────────────────┘\n'));
        if (options.message) {
            console.log(chalk_1.default.yellow('Sending message: ') + options.message + '\n');
        }
    }
    async run() {
        try {
            await this.program.parseAsync();
        }
        catch (error) {
            console.error(chalk_1.default.red(`Error: ${error instanceof Error ? error.message : error}`));
            process.exit(1);
        }
    }
}
// CLI entry point
async function main() {
    const cli = new StandaloneCLI();
    await cli.run();
}
// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log(chalk_1.default.gray('\n👋 Goodbye!'));
    process.exit(0);
});
process.on('SIGTERM', () => {
    console.log(chalk_1.default.gray('\n👋 Goodbye!'));
    process.exit(0);
});
if (require.main === module) {
    main().catch((error) => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
exports.default = StandaloneCLI;
